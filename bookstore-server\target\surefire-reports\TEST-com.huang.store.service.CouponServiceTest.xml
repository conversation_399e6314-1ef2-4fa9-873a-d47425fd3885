<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.huang.store.service.CouponServiceTest" time="2.514" tests="1" errors="1" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="E:\Personal\Homework\SE\bookstore-server\target\test-classes;E:\Personal\Homework\SE\bookstore-server\target\classes;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-logging\2.7.18\spring-boot-starter-logging-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;D:\apache-maven-3.8.8-bin\repository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;D:\apache-maven-3.8.8-bin\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;D:\apache-maven-3.8.8-bin\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\apache-maven-3.8.8-bin\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;D:\apache-maven-3.8.8-bin\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-jdbc\5.3.31\spring-jdbc-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-beans\5.3.31\spring-beans-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-tx\5.3.31\spring-tx-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-thymeleaf\2.7.18\spring-boot-starter-thymeleaf-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\thymeleaf\thymeleaf-spring5\3.0.15.RELEASE\thymeleaf-spring5-3.0.15.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\thymeleaf\thymeleaf\3.0.15.RELEASE\thymeleaf-3.0.15.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\attoparser\attoparser\2.0.5.RELEASE\attoparser-2.0.5.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\unbescape\unbescape\1.1.6.RELEASE\unbescape-1.1.6.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\thymeleaf\extras\thymeleaf-extras-java8time\3.0.4.RELEASE\thymeleaf-extras-java8time-3.0.4.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-web\5.3.31\spring-web-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-webmvc\5.3.31\spring-webmvc-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-context\5.3.31\spring-context-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-expression\5.3.31\spring-expression-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.3.1\mybatis-spring-boot-starter-2.3.1.jar;D:\apache-maven-3.8.8-bin\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.3.1\mybatis-spring-boot-autoconfigure-2.3.1.jar;D:\apache-maven-3.8.8-bin\repository\org\mybatis\mybatis\3.5.13\mybatis-3.5.13.jar;D:\apache-maven-3.8.8-bin\repository\org\mybatis\mybatis-spring\2.1.1\mybatis-spring-2.1.1.jar;D:\apache-maven-3.8.8-bin\repository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-test\2.7.18\spring-boot-starter-test-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-test\2.7.18\spring-boot-test-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.7.18\spring-boot-test-autoconfigure-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;D:\apache-maven-3.8.8-bin\repository\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;D:\apache-maven-3.8.8-bin\repository\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;D:\apache-maven-3.8.8-bin\repository\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\apache-maven-3.8.8-bin\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\apache-maven-3.8.8-bin\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\apache-maven-3.8.8-bin\repository\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;D:\apache-maven-3.8.8-bin\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\mockito\mockito-core\4.5.1\mockito-core-4.5.1.jar;D:\apache-maven-3.8.8-bin\repository\net\bytebuddy\byte-buddy\1.12.23\byte-buddy-1.12.23.jar;D:\apache-maven-3.8.8-bin\repository\net\bytebuddy\byte-buddy-agent\1.12.23\byte-buddy-agent-1.12.23.jar;D:\apache-maven-3.8.8-bin\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\apache-maven-3.8.8-bin\repository\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;D:\apache-maven-3.8.8-bin\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\apache-maven-3.8.8-bin\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-core\5.3.31\spring-core-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-jcl\5.3.31\spring-jcl-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-test\5.3.31\spring-test-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\apache-maven-3.8.8-bin\repository\com\alibaba\fastjson\1.2.4\fastjson-1.2.4.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-devtools\2.1.6.RELEASE\spring-boot-devtools-2.1.6.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\com\alibaba\druid\1.2.20\druid-1.2.20.jar;D:\apache-maven-3.8.8-bin\repository\log4j\log4j\1.2.17\log4j-1.2.17.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-security\2.7.18\spring-boot-starter-security-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-aop\5.3.31\spring-aop-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\security\spring-security-config\5.7.11\spring-security-config-5.7.11.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\security\spring-security-core\5.7.11\spring-security-core-5.7.11.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\security\spring-security-crypto\5.7.11\spring-security-crypto-5.7.11.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\security\spring-security-web\5.7.11\spring-security-web-5.7.11.jar;D:\apache-maven-3.8.8-bin\repository\io\jsonwebtoken\jjwt-api\0.11.5\jjwt-api-0.11.5.jar;D:\apache-maven-3.8.8-bin\repository\io\jsonwebtoken\jjwt-impl\0.11.5\jjwt-impl-0.11.5.jar;D:\apache-maven-3.8.8-bin\repository\io\jsonwebtoken\jjwt-jackson\0.11.5\jjwt-jackson-0.11.5.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-jdbc-spring-boot-starter\4.0.0-RC1\sharding-jdbc-spring-boot-starter-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-jdbc-core\4.0.0-RC1\sharding-jdbc-core-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-entry\4.0.0-RC1\sharding-core-entry-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-api\4.0.0-RC1\sharding-core-api-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-common\4.0.0-RC1\sharding-core-common-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\codehaus\groovy\groovy\2.4.5\groovy-2.4.5-indy.jar;D:\apache-maven-3.8.8-bin\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-common\4.0.0-RC1\sharding-core-parse-common-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-spi\4.0.0-RC1\sharding-core-parse-spi-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\commons\commons-collections4\4.2\commons-collections4-4.2.jar;D:\apache-maven-3.8.8-bin\repository\org\antlr\antlr4-runtime\4.7.1\antlr4-runtime-4.7.1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-mysql\4.0.0-RC1\sharding-core-parse-mysql-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-postgresql\4.0.0-RC1\sharding-core-parse-postgresql-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-oracle\4.0.0-RC1\sharding-core-parse-oracle-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-sqlserver\4.0.0-RC1\sharding-core-parse-sqlserver-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-optimize\4.0.0-RC1\sharding-core-optimize-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-route\4.0.0-RC1\sharding-core-route-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-rewrite\4.0.0-RC1\sharding-core-rewrite-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-execute\4.0.0-RC1\sharding-core-execute-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-merge\4.0.0-RC1\sharding-core-merge-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-transaction-core\4.0.0-RC1\sharding-transaction-core-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\com\google\guava\guava\18.0\guava-18.0.jar;D:\apache-maven-3.8.8-bin\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\apache-maven-3.8.8-bin\repository\org\slf4j\jcl-over-slf4j\1.7.36\jcl-over-slf4j-1.7.36.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-data-redis\2.7.18\spring-boot-starter-data-redis-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\data\spring-data-redis\2.7.18\spring-data-redis-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\data\spring-data-keyvalue\2.7.18\spring-data-keyvalue-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\data\spring-data-commons\2.7.18\spring-data-commons-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-oxm\5.3.31\spring-oxm-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\projectreactor\reactor-core\3.4.34\reactor-core-3.4.34.jar;D:\apache-maven-3.8.8-bin\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-cache\2.7.18\spring-boot-starter-cache-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-context-support\5.3.31\spring-context-support-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-validation\2.7.18\spring-boot-starter-validation-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;D:\apache-maven-3.8.8-bin\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;D:\apache-maven-3.8.8-bin\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\apache-maven-3.8.8-bin\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\JDK\jdk-21.0.5\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire18198438378778113643\surefirebooter2733764507438325362.jar C:\Users\<USER>\AppData\Local\Temp\surefire18198438378778113643 2025-07-02T21-28-30_810-jvmRun1 surefire12754976202032185501tmp surefire_08935313052165407067tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="CouponServiceTest#testDeleteCouponTemplate"/>
    <property name="surefire.test.class.path" value="E:\Personal\Homework\SE\bookstore-server\target\test-classes;E:\Personal\Homework\SE\bookstore-server\target\classes;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-logging\2.7.18\spring-boot-starter-logging-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;D:\apache-maven-3.8.8-bin\repository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;D:\apache-maven-3.8.8-bin\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;D:\apache-maven-3.8.8-bin\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\apache-maven-3.8.8-bin\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;D:\apache-maven-3.8.8-bin\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-jdbc\5.3.31\spring-jdbc-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-beans\5.3.31\spring-beans-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-tx\5.3.31\spring-tx-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-thymeleaf\2.7.18\spring-boot-starter-thymeleaf-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\thymeleaf\thymeleaf-spring5\3.0.15.RELEASE\thymeleaf-spring5-3.0.15.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\thymeleaf\thymeleaf\3.0.15.RELEASE\thymeleaf-3.0.15.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\attoparser\attoparser\2.0.5.RELEASE\attoparser-2.0.5.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\unbescape\unbescape\1.1.6.RELEASE\unbescape-1.1.6.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\thymeleaf\extras\thymeleaf-extras-java8time\3.0.4.RELEASE\thymeleaf-extras-java8time-3.0.4.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-web\5.3.31\spring-web-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-webmvc\5.3.31\spring-webmvc-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-context\5.3.31\spring-context-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-expression\5.3.31\spring-expression-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.3.1\mybatis-spring-boot-starter-2.3.1.jar;D:\apache-maven-3.8.8-bin\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.3.1\mybatis-spring-boot-autoconfigure-2.3.1.jar;D:\apache-maven-3.8.8-bin\repository\org\mybatis\mybatis\3.5.13\mybatis-3.5.13.jar;D:\apache-maven-3.8.8-bin\repository\org\mybatis\mybatis-spring\2.1.1\mybatis-spring-2.1.1.jar;D:\apache-maven-3.8.8-bin\repository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-test\2.7.18\spring-boot-starter-test-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-test\2.7.18\spring-boot-test-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.7.18\spring-boot-test-autoconfigure-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;D:\apache-maven-3.8.8-bin\repository\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;D:\apache-maven-3.8.8-bin\repository\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;D:\apache-maven-3.8.8-bin\repository\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\apache-maven-3.8.8-bin\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\apache-maven-3.8.8-bin\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\apache-maven-3.8.8-bin\repository\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;D:\apache-maven-3.8.8-bin\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\apache-maven-3.8.8-bin\repository\org\mockito\mockito-core\4.5.1\mockito-core-4.5.1.jar;D:\apache-maven-3.8.8-bin\repository\net\bytebuddy\byte-buddy\1.12.23\byte-buddy-1.12.23.jar;D:\apache-maven-3.8.8-bin\repository\net\bytebuddy\byte-buddy-agent\1.12.23\byte-buddy-agent-1.12.23.jar;D:\apache-maven-3.8.8-bin\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\apache-maven-3.8.8-bin\repository\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;D:\apache-maven-3.8.8-bin\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\apache-maven-3.8.8-bin\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-core\5.3.31\spring-core-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-jcl\5.3.31\spring-jcl-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-test\5.3.31\spring-test-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\apache-maven-3.8.8-bin\repository\com\alibaba\fastjson\1.2.4\fastjson-1.2.4.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-devtools\2.1.6.RELEASE\spring-boot-devtools-2.1.6.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\com\alibaba\druid\1.2.20\druid-1.2.20.jar;D:\apache-maven-3.8.8-bin\repository\log4j\log4j\1.2.17\log4j-1.2.17.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-security\2.7.18\spring-boot-starter-security-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-aop\5.3.31\spring-aop-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\security\spring-security-config\5.7.11\spring-security-config-5.7.11.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\security\spring-security-core\5.7.11\spring-security-core-5.7.11.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\security\spring-security-crypto\5.7.11\spring-security-crypto-5.7.11.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\security\spring-security-web\5.7.11\spring-security-web-5.7.11.jar;D:\apache-maven-3.8.8-bin\repository\io\jsonwebtoken\jjwt-api\0.11.5\jjwt-api-0.11.5.jar;D:\apache-maven-3.8.8-bin\repository\io\jsonwebtoken\jjwt-impl\0.11.5\jjwt-impl-0.11.5.jar;D:\apache-maven-3.8.8-bin\repository\io\jsonwebtoken\jjwt-jackson\0.11.5\jjwt-jackson-0.11.5.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;D:\apache-maven-3.8.8-bin\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-jdbc-spring-boot-starter\4.0.0-RC1\sharding-jdbc-spring-boot-starter-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-jdbc-core\4.0.0-RC1\sharding-jdbc-core-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-entry\4.0.0-RC1\sharding-core-entry-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-api\4.0.0-RC1\sharding-core-api-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-common\4.0.0-RC1\sharding-core-common-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\codehaus\groovy\groovy\2.4.5\groovy-2.4.5-indy.jar;D:\apache-maven-3.8.8-bin\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-common\4.0.0-RC1\sharding-core-parse-common-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-spi\4.0.0-RC1\sharding-core-parse-spi-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\commons\commons-collections4\4.2\commons-collections4-4.2.jar;D:\apache-maven-3.8.8-bin\repository\org\antlr\antlr4-runtime\4.7.1\antlr4-runtime-4.7.1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-mysql\4.0.0-RC1\sharding-core-parse-mysql-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-postgresql\4.0.0-RC1\sharding-core-parse-postgresql-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-oracle\4.0.0-RC1\sharding-core-parse-oracle-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-parse-sqlserver\4.0.0-RC1\sharding-core-parse-sqlserver-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-optimize\4.0.0-RC1\sharding-core-optimize-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-route\4.0.0-RC1\sharding-core-route-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-rewrite\4.0.0-RC1\sharding-core-rewrite-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-execute\4.0.0-RC1\sharding-core-execute-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-core-merge\4.0.0-RC1\sharding-core-merge-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\shardingsphere\sharding-transaction-core\4.0.0-RC1\sharding-transaction-core-4.0.0-RC1.jar;D:\apache-maven-3.8.8-bin\repository\com\google\guava\guava\18.0\guava-18.0.jar;D:\apache-maven-3.8.8-bin\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\apache-maven-3.8.8-bin\repository\org\slf4j\jcl-over-slf4j\1.7.36\jcl-over-slf4j-1.7.36.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-data-redis\2.7.18\spring-boot-starter-data-redis-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\data\spring-data-redis\2.7.18\spring-data-redis-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\data\spring-data-keyvalue\2.7.18\spring-data-keyvalue-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\data\spring-data-commons\2.7.18\spring-data-commons-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-oxm\5.3.31\spring-oxm-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;D:\apache-maven-3.8.8-bin\repository\io\projectreactor\reactor-core\3.4.34\reactor-core-3.4.34.jar;D:\apache-maven-3.8.8-bin\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-cache\2.7.18\spring-boot-starter-cache-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\spring-context-support\5.3.31\spring-context-support-5.3.31.jar;D:\apache-maven-3.8.8-bin\repository\org\springframework\boot\spring-boot-starter-validation\2.7.18\spring-boot-starter-validation-2.7.18.jar;D:\apache-maven-3.8.8-bin\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;D:\apache-maven-3.8.8-bin\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;D:\apache-maven-3.8.8-bin\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\apache-maven-3.8.8-bin\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;D:\apache-maven-3.8.8-bin\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\JDK\jdk-21.0.5"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="E:\Personal\Homework\SE\bookstore-server"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire18198438378778113643\surefirebooter2733764507438325362.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.5+9-LTS-239"/>
    <property name="user.name" value="a2241"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\apache-maven-3.8.8-bin\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="21.0.5"/>
    <property name="user.dir" value="E:\Personal\Homework\SE\bookstore-server"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\JDK\jdk-21.0.5\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\miniconda3;D:\miniconda3\Library\mingw-w64\bin;D:\miniconda3\Library\usr\bin;D:\miniconda3\Library\bin;D:\miniconda3\Scripts;D:\miniconda3\bin;D:\miniconda3\condabin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\dotnet;D:\JDK\jdk-21.0.5\bin;D:\apache-tomcat-10.1.39-windows-x64\apache-tomcat-10.1.39\lib;D:\apache-tomcat-10.1.39-windows-x64\apache-tomcat-10.1.39\bin;D:\apache-tomcat-10.1.39-windows-x64\apache-tomcat-10.1.39\lib\servlet-api.jar;D:\TDM-GCC\bin;D:\Git\cmd;D:\apache-maven-3.8.8-bin\apache-maven-3.8.8\bin;D:\mysql-8.4.4-winx64\mysql-8.4.4-winx64\bin;D:\Windows Kits\10\Windows Performance Toolkit;C:\Program Files\Docker\Docker\resources\bin;D:\miniconda3;D:\miniconda3\Scripts;D:\miniconda3\Library\bin;D:\nvm\nvm;D:\nvm\nodejs;D:\nvm\nodejs\node_global;D:\Redis;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\VSCode\Microsoft VS Code\bin;D:\pycharm\PyCharm 2024.3.4\bin;.;D:\nvm\nvm;D:\nvm\nodejs;D:\nvm\nodejs\node_global;D:\nvm\nodejs\node_global\node_modules\yarn\bin;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.5+9-LTS-239"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
  </properties>
  <testcase name="testDeleteCouponTemplate" classname="com.huang.store.service.CouponServiceTest" time="0.011">
    <error message="Failed to load ApplicationContext" type="java.lang.IllegalStateException">java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'dataSource' defined in class path resource [org/apache/shardingsphere/shardingjdbc/spring/boot/SpringBootConfiguration.class]: Cannot register bean definition [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=org.apache.shardingsphere.shardingjdbc.spring.boot.SpringBootConfiguration; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [org/apache/shardingsphere/shardingjdbc/spring/boot/SpringBootConfiguration.class]] for bean 'dataSource': There is already [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; factoryBeanName=com.huang.store.configure.DruidConfiguration$IDataSourceProperties; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/huang/store/configure/DruidConfiguration$IDataSourceProperties.class]] bound.
</error>
    <system-out><![CDATA[21:28:35.709 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating CacheAwareContextLoaderDelegate from class [org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate]
21:28:35.719 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating BootstrapContext using constructor [public org.springframework.test.context.support.DefaultBootstrapContext(java.lang.Class,org.springframework.test.context.CacheAwareContextLoaderDelegate)]
21:28:35.757 [main] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating TestContextBootstrapper for test class [com.huang.store.service.CouponServiceTest] from class [org.springframework.boot.test.context.SpringBootTestContextBootstrapper]
21:28:35.782 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.huang.store.service.CouponServiceTest], using SpringBootContextLoader
21:28:35.787 [main] DEBUG org.springframework.test.context.support.AbstractContextLoader - Did not detect default resource location for test class [com.huang.store.service.CouponServiceTest]: class path resource [com/huang/store/service/CouponServiceTest-context.xml] does not exist
21:28:35.788 [main] DEBUG org.springframework.test.context.support.AbstractContextLoader - Did not detect default resource location for test class [com.huang.store.service.CouponServiceTest]: class path resource [com/huang/store/service/CouponServiceTestContext.groovy] does not exist
21:28:35.788 [main] INFO org.springframework.test.context.support.AbstractContextLoader - Could not detect default resource locations for test class [com.huang.store.service.CouponServiceTest]: no resource found for suffixes {-context.xml, Context.groovy}.
21:28:35.789 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.huang.store.service.CouponServiceTest]: CouponServiceTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
21:28:35.974 [main] DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider - Identified candidate component class: file [E:\Personal\Homework\SE\bookstore-server\target\classes\com\huang\store\StoreApplication.class]
21:28:35.975 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.huang.store.StoreApplication for test class com.huang.store.service.CouponServiceTest
21:28:36.094 [main] DEBUG org.springframework.boot.test.context.SpringBootTestContextBootstrapper - @TestExecutionListeners is not present for class [com.huang.store.service.CouponServiceTest]: using defaults.
21:28:36.094 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
21:28:36.127 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@63611043, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@20ca951f, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@2d778add, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@73302995, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@1838ccb8, org.springframework.test.context.support.DirtiesContextTestExecutionListener@6c2ed0cd, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7d9e8ef7, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@f107c50, org.springframework.test.context.event.EventPublishingTestExecutionListener@51133c06, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@4b213651, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@4241e0f4, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@4ebff610, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@60410cd, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@44d52de2, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@95e33cc]
21:28:36.131 [main] DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener - Before test class: context [DefaultTestContext@6f44a157 testClass = CouponServiceTest, testInstance = [null], testMethod = [null], testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@6bc407fd testClass = CouponServiceTest, locations = '{}', classes = '{class com.huang.store.StoreApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@72cde7cc, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@2bfc268b, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@a2431d0, org.springframework.boot.test.autoconfigure.actuate.metrics.MetricsExportContextCustomizerFactory$DisableMetricExportContextCustomizer@550ee7e5, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@2b48a640, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@7494e528], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true]], class annotated with @DirtiesContext [false] with mode [null].

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v2.7.18)

2025-07-02 21:28:36.709 [main] INFO  com.huang.store.service.CouponServiceTest - Starting CouponServiceTest using Java 21.0.5 on OLIVIA with PID 24416 (started by a2241 in E:\Personal\Homework\SE\bookstore-server)
2025-07-02 21:28:36.710 [main] INFO  com.huang.store.service.CouponServiceTest - The following 1 profile is active: "test"
2025-07-02 21:28:36.719 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-02 21:28:38.087 [main] WARN  o.s.w.context.support.GenericWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'dataSource' defined in class path resource [org/apache/shardingsphere/shardingjdbc/spring/boot/SpringBootConfiguration.class]: Cannot register bean definition [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=org.apache.shardingsphere.shardingjdbc.spring.boot.SpringBootConfiguration; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [org/apache/shardingsphere/shardingjdbc/spring/boot/SpringBootConfiguration.class]] for bean 'dataSource': There is already [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; factoryBeanName=com.huang.store.configure.DruidConfiguration$IDataSourceProperties; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/huang/store/configure/DruidConfiguration$IDataSourceProperties.class]] bound.
2025-07-02 21:28:38.122 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'dataSource', defined in class path resource [org/apache/shardingsphere/shardingjdbc/spring/boot/SpringBootConfiguration.class], could not be registered. A bean with that name has already been defined in class path resource [com/huang/store/configure/DruidConfiguration$IDataSourceProperties.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

2025-07-02 21:28:38.126 [main] ERROR o.springframework.test.context.TestContextManager - Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@63611043] to prepare test instance [com.huang.store.service.CouponServiceTest@194d329e]
java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:98)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:124)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:248)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:138)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$8(ClassBasedTestDescriptor.java:363)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:368)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$9(ClassBasedTestDescriptor.java:363)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:310)
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:735)
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:734)
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:362)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$instantiateAndPostProcessTestInstance$6(ClassBasedTestDescriptor.java:283)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:282)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$4(ClassBasedTestDescriptor.java:272)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$5(ClassBasedTestDescriptor.java:271)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:31)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:102)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:101)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:66)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$2(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:90)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124)
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418)
Caused by: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'dataSource' defined in class path resource [org/apache/shardingsphere/shardingjdbc/spring/boot/SpringBootConfiguration.class]: Cannot register bean definition [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=org.apache.shardingsphere.shardingjdbc.spring.boot.SpringBootConfiguration; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [org/apache/shardingsphere/shardingjdbc/spring/boot/SpringBootConfiguration.class]] for bean 'dataSource': There is already [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=true; factoryBeanName=com.huang.store.configure.DruidConfiguration$IDataSourceProperties; factoryMethodName=dataSource; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/huang/store/configure/DruidConfiguration$IDataSourceProperties.class]] bound.
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.registerBeanDefinition(DefaultListableBeanFactory.java:1006)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForBeanMethod(ConfigurationClassBeanDefinitionReader.java:295)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:153)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:129)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:343)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:247)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:311)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:112)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:756)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:572)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:136)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:141)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:90)
	... 67 common frames omitted
]]></system-out>
  </testcase>
</testsuite>