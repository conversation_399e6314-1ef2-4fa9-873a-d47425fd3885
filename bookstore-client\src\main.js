// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css';
import store from './store/store'
import axios from 'axios'

// 引入全局样式 (暂时注释，等SCSS配置完成)
// import './assets/styles/global.scss'

import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'
//引入echarts
import echarts from "echarts"

import "./axios"
import "./permission"

Vue.prototype.$echarts = echarts
Vue.use(mavonEditor)
Vue.config.productionTip = false
Vue.use(ElementUI)
Vue.prototype.$http = axios  //修改内部的$http为axios  $http.get("")



/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  components: { App },
  store,
  template: '<App/>'
})

Date.prototype.format = function (format) {
  let args = {
    "M+": this.getMonth() + 1,
    "d+": this.getDate(),
    "h+": this.getHours(),
    "m+": this.getMinutes(),
    "s+": this.getSeconds(),
    "q+": Math.floor((this.getMonth() + 3) / 3),  //quarter
    "S": this.getMilliseconds()
  };
  if (/(y+)/.test(format))
    format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  for (let i in args) {
    let n = args[i];
    if (new RegExp("(" + i + ")").test(format))
      format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length));
  }
  return format;
};
