# 优惠券系统前端测试指南

## 📋 测试准备

### 1. 启动后端服务
```bash
cd bookstore-server
mvn spring-boot:run
```

### 2. 启动前端服务
```bash
cd bookstore-client
npm run serve
```

### 3. 确保数据库已初始化
确保已执行优惠券系统的SQL脚本，数据库中有示例优惠券数据。

## 🧪 测试流程

### 一、管理员功能测试

#### 1. 登录管理员账号
- 访问 `http://localhost:8080/login`
- 使用管理员账号登录

#### 2. 测试优惠券管理页面
- 访问 `http://localhost:8080/admin/coupon`
- 验证页面是否正常加载
- 检查优惠券列表是否显示

#### 3. 测试创建优惠券
**创建满减券：**
- 点击"创建优惠券"按钮
- 填写表单：
  - 优惠券名称：测试满减券
  - 优惠券描述：满100减20测试券
  - 优惠券类型：满减券
  - 减免金额：20
  - 最低消费：100
  - 发放数量：50
  - 每人限领：1
  - 有效天数：30
- 点击"创建"按钮
- 验证创建成功

**创建折扣券：**
- 点击"创建优惠券"按钮
- 填写表单：
  - 优惠券名称：测试折扣券
  - 优惠券描述：8.5折测试券
  - 优惠券类型：折扣券
  - 折扣百分比：85
  - 最低消费：50
  - 最大折扣：30
  - 发放数量：30
  - 每人限领：2
  - 有效天数：15
- 点击"创建"按钮
- 验证创建成功

#### 4. 测试优惠券管理功能
- 测试编辑优惠券
- 测试启用/停用优惠券
- 测试删除优惠券（谨慎操作）

### 二、用户功能测试

#### 1. 登录用户账号
- 退出管理员账号
- 使用普通用户账号登录

#### 2. 测试优惠券中心
- 访问 `http://localhost:8080/user/coupons`
- 验证页面是否正常加载
- 检查"可领取"标签页是否显示可用优惠券

#### 3. 测试领取优惠券
- 在"可领取"标签页中选择优惠券
- 点击"立即领取"按钮
- 验证领取成功提示
- 切换到"我的优惠券"标签页
- 验证优惠券是否出现在列表中

#### 4. 测试优惠券筛选
- 在"我的优惠券"标签页中测试筛选功能：
  - 全部
  - 未使用
  - 已使用
  - 已过期

### 三、购买流程测试

#### 1. 添加商品到购物车
- 浏览商品页面
- 添加商品到购物车

#### 2. 进入购买页面
- 从购物车进入购买页面
- 或直接购买商品

#### 3. 测试优惠券选择
- 在购买页面查看优惠券选择区域
- 点击优惠券选择区域
- 验证优惠券选择对话框是否弹出
- 检查可用优惠券列表
- 验证不可用优惠券的原因显示

#### 4. 测试优惠券使用
**测试满减券：**
- 选择满减券
- 验证折扣金额计算是否正确
- 验证最终价格是否正确更新

**测试折扣券：**
- 选择折扣券
- 验证折扣金额计算是否正确
- 验证最大折扣限制是否生效

**测试不使用优惠券：**
- 选择"不使用优惠券"
- 验证价格恢复原价

#### 5. 测试订单提交
- 选择收货地址
- 选择优惠券
- 点击"立刻下单"
- 验证订单提交成功
- 检查优惠券状态是否变为"已使用"

## 🔍 测试检查点

### 界面检查
- [ ] 所有页面正常加载，无JavaScript错误
- [ ] 优惠券卡片样式正确显示
- [ ] 响应式布局在不同屏幕尺寸下正常
- [ ] 图标和文字对齐正确

### 功能检查
- [ ] 管理员可以创建、编辑、删除优惠券
- [ ] 用户可以查看和领取优惠券
- [ ] 优惠券筛选功能正常
- [ ] 购买页面优惠券选择功能正常
- [ ] 折扣计算准确
- [ ] 订单提交包含优惠券信息

### 数据检查
- [ ] 优惠券状态正确更新
- [ ] 优惠券使用记录正确保存
- [ ] 订单中包含正确的优惠券信息
- [ ] 库存数量正确减少

### 错误处理检查
- [ ] 网络错误时显示适当提示
- [ ] 表单验证正确工作
- [ ] 优惠券不可用时显示原因
- [ ] 重复领取时显示错误提示

## 🐛 常见问题排查

### 1. 页面无法加载
- 检查后端服务是否启动
- 检查API接口是否正常
- 查看浏览器控制台错误信息

### 2. 优惠券列表为空
- 检查数据库中是否有优惠券数据
- 检查API接口返回数据
- 验证用户权限

### 3. 折扣计算错误
- 检查前端计算逻辑
- 验证后端API返回的计算结果
- 确认优惠券类型和参数设置

### 4. 优惠券无法使用
- 检查优惠券是否过期
- 验证订单金额是否满足使用条件
- 确认优惠券状态是否正常

## 📊 测试数据示例

### 测试用优惠券模板
```json
{
  "name": "新用户专享券",
  "description": "新用户注册即可获得20元优惠券",
  "type": 1,
  "discountValue": 20.00,
  "minOrderAmount": 100.00,
  "totalQuantity": 100,
  "perUserLimit": 1,
  "validDays": 30
}
```

### 测试用订单数据
- 订单金额：150元（满足满100减20的条件）
- 订单金额：80元（不满足满100减20的条件）
- 订单金额：200元（测试折扣券上限）

## 📝 测试报告模板

### 测试结果记录
- 测试时间：
- 测试环境：
- 测试人员：

### 功能测试结果
| 功能模块 | 测试结果 | 问题描述 | 严重程度 |
|---------|---------|---------|---------|
| 管理员创建优惠券 | ✅/❌ | | |
| 用户领取优惠券 | ✅/❌ | | |
| 优惠券选择使用 | ✅/❌ | | |
| 折扣计算 | ✅/❌ | | |
| 订单提交 | ✅/❌ | | |

### 总体评价
- 功能完整性：
- 用户体验：
- 性能表现：
- 建议改进：

---

**注意**：测试过程中如发现问题，请详细记录错误信息、复现步骤和环境信息，以便开发人员快速定位和修复问题。
