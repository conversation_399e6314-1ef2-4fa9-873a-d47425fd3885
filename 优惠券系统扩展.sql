-- ========================================
-- 优惠券系统扩展SQL脚本
-- ========================================

-- 1. 优惠券模板表（管理员创建的优惠券类型）
CREATE TABLE `coupon_template` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '优惠券模板ID',
    `name` varchar(100) NOT NULL COMMENT '优惠券名称',
    `description` varchar(255) DEFAULT NULL COMMENT '优惠券描述',
    `type` tinyint(2) NOT NULL COMMENT '优惠券类型：1-满减券，2-折扣券，3-免邮券',
    `discount_type` tinyint(2) NOT NULL COMMENT '折扣类型：1-固定金额，2-百分比折扣',
    `discount_value` decimal(10,2) NOT NULL COMMENT '折扣值（金额或百分比）',
    `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低消费金额',
    `max_discount_amount` decimal(10,2) DEFAULT NULL COMMENT '最大折扣金额（折扣券用）',
    `total_quantity` int(11) NOT NULL COMMENT '发放总数量',
    `used_quantity` int(11) DEFAULT '0' COMMENT '已使用数量',
    `per_user_limit` int(11) DEFAULT '1' COMMENT '每用户限领数量',
    `valid_start_time` timestamp NOT NULL COMMENT '有效期开始时间',
    `valid_end_time` timestamp NOT NULL COMMENT '有效期结束时间',
    `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-停用，1-启用',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`),
    KEY `idx_status` (`status`),
    KEY `idx_valid_time` (`valid_start_time`, `valid_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券模板表';

-- 2. 用户优惠券表（用户领取的优惠券实例）
CREATE TABLE `user_coupon` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户优惠券ID',
    `coupon_template_id` int(11) NOT NULL COMMENT '优惠券模板ID',
    `account` varchar(100) NOT NULL COMMENT '用户账号',
    `coupon_code` varchar(50) NOT NULL COMMENT '优惠券码',
    `status` tinyint(2) DEFAULT '1' COMMENT '状态：1-未使用，2-已使用，3-已过期',
    `receive_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
    `use_time` timestamp NULL DEFAULT NULL COMMENT '使用时间',
    `order_id` varchar(50) DEFAULT NULL COMMENT '使用的订单号',
    `expire_time` timestamp NOT NULL COMMENT '过期时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_coupon_code` (`coupon_code`),
    KEY `idx_account` (`account`),
    KEY `idx_template_id` (`coupon_template_id`),
    KEY `idx_status` (`status`),
    KEY `idx_expire_time` (`expire_time`),
    CONSTRAINT `fk_user_coupon_template` FOREIGN KEY (`coupon_template_id`) REFERENCES `coupon_template` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';

-- 3. 优惠券使用记录表（详细的使用记录）
CREATE TABLE `coupon_usage_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '使用记录ID',
    `user_coupon_id` int(11) NOT NULL COMMENT '用户优惠券ID',
    `order_id` varchar(50) NOT NULL COMMENT '订单号',
    `account` varchar(100) NOT NULL COMMENT '用户账号',
    `original_amount` decimal(10,2) NOT NULL COMMENT '原始金额',
    `discount_amount` decimal(10,2) NOT NULL COMMENT '优惠金额',
    `final_amount` decimal(10,2) NOT NULL COMMENT '最终金额',
    `use_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_coupon_id` (`user_coupon_id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_account` (`account`),
    CONSTRAINT `fk_coupon_usage_user_coupon` FOREIGN KEY (`user_coupon_id`) REFERENCES `user_coupon` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券使用记录表';

-- ========================================
-- 修改现有表结构
-- ========================================

-- 4. 修改expense表，增加优惠券相关字段
ALTER TABLE `expense` 
ADD COLUMN `coupon_id` int(11) DEFAULT NULL COMMENT '使用的优惠券ID' AFTER `coupon`,
ADD COLUMN `coupon_discount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠券折扣金额' AFTER `coupon_id`,
ADD INDEX `idx_coupon_id` (`coupon_id`);

-- 为expense表添加外键约束（可选，根据需要决定是否添加）
-- ALTER TABLE `expense` 
-- ADD CONSTRAINT `fk_expense_user_coupon` FOREIGN KEY (`coupon_id`) REFERENCES `user_coupon` (`id`);

-- 5. 修改bookorder表，增加优惠券字段（可选）
ALTER TABLE `bookorder` 
ADD COLUMN `coupon_ids` varchar(255) DEFAULT NULL COMMENT '使用的优惠券ID列表（逗号分隔）' AFTER `beUserDelete`;

-- ========================================
-- 插入示例数据
-- ========================================

-- 插入优惠券模板示例数据
INSERT INTO `coupon_template` (`name`, `description`, `type`, `discount_type`, `discount_value`, `min_order_amount`, `max_discount_amount`, `total_quantity`, `per_user_limit`, `valid_start_time`, `valid_end_time`) VALUES
('新用户专享券', '新用户注册即可获得20元优惠券', 1, 1, 20.00, 100.00, NULL, 1000, 1, '2024-01-01 00:00:00', '2024-12-31 23:59:59'),
('满200减30', '订单满200元可使用，减免30元', 1, 1, 30.00, 200.00, NULL, 500, 2, '2024-01-01 00:00:00', '2024-06-30 23:59:59'),
('图书节8折券', '图书节期间享受8折优惠', 2, 2, 80.00, 50.00, 50.00, 200, 1, '2024-04-01 00:00:00', '2024-04-30 23:59:59'),
('免邮券', '订单免运费', 3, 1, 10.00, 0.00, NULL, 2000, 5, '2024-01-01 00:00:00', '2024-12-31 23:59:59');

-- 插入用户优惠券示例数据（为已有用户分配优惠券）
INSERT INTO `user_coupon` (`coupon_template_id`, `account`, `coupon_code`, `expire_time`) VALUES
(1, '<EMAIL>', 'NEWUSER001', '2024-12-31 23:59:59'),
(2, '<EMAIL>', 'SAVE30001', '2024-06-30 23:59:59'),
(1, '<EMAIL>', 'NEWUSER002', '2024-12-31 23:59:59'),
(4, '<EMAIL>', 'FREESHIP001', '2024-12-31 23:59:59');

-- ========================================
-- 创建视图和存储过程（可选）
-- ========================================

-- 创建用户可用优惠券视图
CREATE VIEW `user_available_coupons` AS
SELECT 
    uc.id as user_coupon_id,
    uc.account,
    uc.coupon_code,
    ct.name as coupon_name,
    ct.description,
    ct.type,
    ct.discount_type,
    ct.discount_value,
    ct.min_order_amount,
    ct.max_discount_amount,
    uc.receive_time,
    uc.expire_time
FROM user_coupon uc
JOIN coupon_template ct ON uc.coupon_template_id = ct.id
WHERE uc.status = 1 
  AND uc.expire_time > NOW()
  AND ct.status = 1;

-- ========================================
-- 索引优化建议
-- ========================================

-- 为高频查询添加复合索引
CREATE INDEX `idx_account_status_expire` ON `user_coupon` (`account`, `status`, `expire_time`);
CREATE INDEX `idx_template_status_valid` ON `coupon_template` (`status`, `valid_start_time`, `valid_end_time`);

-- ========================================
-- 使用说明
-- ========================================

/*
优惠券系统使用流程：

1. 管理员创建优惠券模板（coupon_template表）
2. 用户领取优惠券，生成用户优惠券记录（user_coupon表）
3. 用户下单时选择优惠券，系统验证并计算折扣
4. 订单确认后，更新user_coupon状态为已使用，记录使用日志（coupon_usage_log表）
5. 更新expense表中的优惠券相关字段

优惠券类型说明：
- type=1: 满减券（满X元减Y元）
- type=2: 折扣券（打X折，最多减Y元）
- type=3: 免邮券（免运费）

discount_type说明：
- 1: 固定金额（如减20元）
- 2: 百分比折扣（如80表示8折）
*/
