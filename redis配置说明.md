# Redis配置说明

## 项目Redis配置要求

根据项目配置文件 `application-dev.yml`，Redis需要以下配置：

- **端口**: 6380 (非默认端口)
- **密码**: root
- **主机**: 127.0.0.1

## 配置方法

### 方法1: 修改Redis配置文件

1. 找到Redis配置文件 `redis.conf`
2. 修改以下配置项：

```conf
# 端口配置
port 6380

# 密码配置
requirepass root

# 绑定地址
bind 127.0.0.1
```

3. 使用配置文件启动Redis：
```bash
redis-server redis.conf
```

### 方法2: 启动时指定参数

```bash
redis-server --port 6380 --requirepass root
```

### 方法3: 修改项目配置（推荐）

如果您希望使用Redis的默认配置，可以修改项目配置文件：

编辑 `bookstore-server/src/main/resources/application-dev.yml`：

```yaml
spring:
  redis:
    host: 127.0.0.1
    port: 6379        # 改为默认端口
    password:         # 如果没有密码，留空或删除此行
    database: 0
```

## 验证Redis连接

启动Redis后，可以使用以下命令验证：

```bash
# 连接Redis（如果使用密码）
redis-cli -p 6380 -a root

# 连接Redis（默认配置）
redis-cli

# 测试连接
ping
# 应该返回 PONG
```

## 推荐配置

为了简化配置，建议：

1. 使用Redis默认端口6379
2. 开发环境不设置密码
3. 修改项目配置文件匹配Redis设置

这样可以避免额外的Redis配置工作。
