# 数据库初始化步骤

## 重要提醒
在启动项目之前，必须先初始化数据库！

## 步骤1: 启动MySQL服务

```bash
# Windows
net start mysql

# 或者通过服务管理器启动MySQL服务
```

## 步骤2: 连接MySQL并创建数据库

打开命令行，执行以下命令：

```bash
# 连接到MySQL（输入您的root密码）
mysql -u root -p
```

在MySQL命令行中执行：

```sql
-- 创建数据库
DROP DATABASE IF EXISTS bookstore;
CREATE DATABASE bookstore CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE bookstore;

-- 验证数据库创建成功
SHOW DATABASES LIKE 'bookstore';
```

## 步骤3: 导入表结构

在命令行中执行（不是在MySQL命令行中）：

```bash
# 进入项目目录
cd /d E:\Personal\Homework\SE

# 导入表结构
mysql -u root -p bookstore < bookstore-server/src/main/resources/database/bookstore_ddl.sql
```

## 步骤4: 导入示例数据

```bash
# 导入示例数据
mysql -u root -p bookstore < bookstore-server/src/main/resources/database/bookstore_sample_data.sql
```

## 步骤5: 验证数据导入

连接到MySQL并验证：

```bash
mysql -u root -p
```

```sql
USE bookstore;

-- 查看所有表
SHOW TABLES;

-- 检查数据
SELECT COUNT(*) as user_count FROM user;
SELECT COUNT(*) as book_count FROM book;
SELECT COUNT(*) as order_count FROM bookorder;

-- 查看用户表数据
SELECT id, username, email FROM user LIMIT 5;
```

## 步骤6: 修改数据库配置（如需要）

如果您的MySQL密码不是 `root`，请修改配置文件：

编辑 `bookstore-server/src/main/resources/application-dev.yml`：

```yaml
spring:
  shardingsphere:
    datasource:
      master:
        username: root
        password: 您的MySQL密码  # 修改这里
      slave:
        username: root
        password: 您的MySQL密码  # 修改这里
```

## 常见问题解决

### 问题1: 字符集错误
```sql
-- 重新创建数据库并指定字符集
DROP DATABASE IF EXISTS bookstore;
CREATE DATABASE bookstore CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 问题2: 权限不足
```sql
-- 授予权限
GRANT ALL PRIVILEGES ON bookstore.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

### 问题3: 导入脚本失败
- 确保文件路径正确
- 检查MySQL服务是否正常运行
- 确认用户名密码正确

## 完成后

数据库初始化完成后，您可以：

1. 运行 `启动项目.bat` 启动整个系统
2. 或者手动启动后端和前端服务

## 默认测试账号

系统包含以下测试账号：

| 用户名 | 密码 | 角色 |
|--------|------|------|
| <EMAIL> | 123456 | 管理员 |
| <EMAIL> | 123456 | 普通用户 |
| <EMAIL> | 123456 | 普通用户 |

**注意**: 密码在数据库中已经过BCrypt加密处理。
