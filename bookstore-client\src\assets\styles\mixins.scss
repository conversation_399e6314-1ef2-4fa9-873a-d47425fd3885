// 📚 书店管理系统 - SCSS 混入
// ==========================================

@import './variables.scss';

// 📱 响应式混入
// ------------------------------------------

@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: $breakpoint-xs) { @content; }
  }
  @if $breakpoint == sm {
    @media (max-width: $breakpoint-sm) { @content; }
  }
  @if $breakpoint == md {
    @media (max-width: $breakpoint-md) { @content; }
  }
  @if $breakpoint == lg {
    @media (max-width: $breakpoint-lg) { @content; }
  }
  @if $breakpoint == xl {
    @media (max-width: $breakpoint-xl) { @content; }
  }
}

// 🎨 按钮混入
// ------------------------------------------

@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-base;
  border: none;
  border-radius: $border-radius-base;
  font-family: $font-family-primary;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  cursor: pointer;
  transition: all $transition-base;
  text-decoration: none;
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.2);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background-color: $primary-color;
  color: white;
  
  &:hover:not(:disabled) {
    background-color: darken($primary-color, 10%);
    transform: translateY(-1px);
    box-shadow: $shadow-base;
  }
  
  &:active {
    transform: translateY(0);
  }
}

@mixin button-secondary {
  @include button-base;
  background-color: transparent;
  color: $primary-color;
  border: 1px solid $primary-color;
  
  &:hover:not(:disabled) {
    background-color: $primary-color;
    color: white;
  }
}

// 📦 卡片混入
// ------------------------------------------

@mixin card-base {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  transition: all $transition-base;
  overflow: hidden;
  
  &:hover {
    box-shadow: $shadow-base;
    transform: translateY(-2px);
  }
}

@mixin card-content {
  padding: $spacing-lg;
}

// 🔤 文字混入
// ------------------------------------------

@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 🎯 居中混入
// ------------------------------------------

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 📐 容器混入
// ------------------------------------------

@mixin container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-base;
  
  @include respond-to(sm) {
    padding: 0 $spacing-sm;
  }
}

// 🎨 渐变混入
// ------------------------------------------

@mixin gradient-primary {
  background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
}

@mixin gradient-overlay {
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
}
