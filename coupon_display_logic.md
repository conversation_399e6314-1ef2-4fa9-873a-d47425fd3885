# 优惠券折扣描述显示逻辑

## 概述

本文档说明了优惠券系统中不同类型优惠券的折扣描述显示逻辑，特别是折扣券根据是否有使用门槛显示不同描述的实现。

## 显示逻辑

### 满减券（type = 1）
**显示规则**: 始终显示"满X元可用"
```javascript
// 满减券：显示"满X元可用"
return `满${template.minOrderAmount || 0}元可用`
```

**示例**:
- 满100减20券 → "满100元可用"
- 满50减10券 → "满50元可用"
- 满0减5券 → "满0元可用"

### 折扣券（type = 2）
**显示规则**: 根据是否有使用门槛显示不同描述

#### 有门槛的折扣券
```javascript
if (template.minOrderAmount && template.minOrderAmount > 0) {
  return `满${template.minOrderAmount}元可用`
}
```

**示例**:
- 8.5折券，满100元可用 → "满100元可用"
- 9折券，满50元可用 → "满50元可用"

#### 无门槛的折扣券
```javascript
else {
  return '无门槛'
}
```

**示例**:
- 8.5折券，无最低消费 → "无门槛"
- 9.5折券，最低消费为0 → "无门槛"

## 实现位置

### 用户端优惠券中心
文件: `src/pages/UserHome/CouponCenter.vue`

#### 可领取优惠券
```javascript
formatConditionDesc(template) {
  if (template.type === 1) {
    // 满减券：显示"满X元可用"
    return `满${template.minOrderAmount || 0}元可用`
  } else {
    // 折扣券：根据是否有门槛显示不同描述
    if (template.minOrderAmount && template.minOrderAmount > 0) {
      return `满${template.minOrderAmount}元可用`
    } else {
      return '无门槛'
    }
  }
}
```

#### 我的优惠券
```javascript
formatMyCouponConditionDesc(coupon) {
  if (coupon.type === 1) {
    // 满减券：显示"满X元可用"
    return `满${coupon.minOrderAmount || 0}元可用`
  } else {
    // 折扣券：根据是否有门槛显示不同描述
    if (coupon.minOrderAmount && coupon.minOrderAmount > 0) {
      return `满${coupon.minOrderAmount}元可用`
    } else {
      return '无门槛'
    }
  }
}
```

### 管理员端优惠券管理
文件: `src/pages/AdminHome/CouponManagement.vue`

#### 优惠券列表显示
```javascript
formatCouponCondition(template) {
  if (template.type === 1) {
    // 满减券：显示"满X元可用"
    return `满${template.minOrderAmount || 0}元可用`
  } else {
    // 折扣券：根据是否有门槛显示不同描述
    if (template.minOrderAmount && template.minOrderAmount > 0) {
      return `满${template.minOrderAmount}元可用`
    } else {
      return '无门槛'
    }
  }
}
```

## 显示效果示例

### 用户端优惠券中心

#### 可领取优惠券
```
┌─────────────────────────────────────────────────────────────────────┐
│ 新用户专享券              ¥20 元                剩余 50             │
│ 有效期: 30天             满100元可用             [立即领取]         │ ← 满减券
├─────────────────────────────────────────────────────────────────────┤
│ 8.5折优惠券              8.5 折               剩余 30             │
│ 有效期: 30天             满50元可用              [立即领取]         │ ← 有门槛折扣券
├─────────────────────────────────────────────────────────────────────┤
│ 新人折扣券               9 折                 剩余 100            │
│ 有效期: 30天             无门槛                  [立即领取]         │ ← 无门槛折扣券
└─────────────────────────────────────────────────────────────────────┘
```

#### 我的优惠券
```
┌─────────────────────────────────────────────────────────────────────┐
│ ¥20 元             新用户专享券          有效期至: 2024-12-31        │
│ 满100元可用                                                        │ ← 满减券
├─────────────────────────────────────────────────────────────────────┤
│ 8.5 折             8.5折优惠券          有效期至: 2024-12-31        │
│ 满50元可用                                                         │ ← 有门槛折扣券
├─────────────────────────────────────────────────────────────────────┤
│ 9 折               新人折扣券            有效期至: 2024-12-31        │
│ 无门槛                                                             │ ← 无门槛折扣券
└─────────────────────────────────────────────────────────────────────┘
```

### 管理员端优惠券管理

#### 优惠券列表
```
| ID | 优惠券名称    | 类型   | 优惠信息      | 库存信息      | 使用率 | 状态 |
|----|-------------|--------|--------------|--------------|--------|------|
| 1  | 新用户专享券  | 满减券  | 减20元       | 总量: 100    | 15%   | 启用 |
|    |             |        | 满100元可用   | 已领: 15     |       |      |
|----|-------------|--------|--------------|--------------|--------|------|
| 2  | 8.5折优惠券  | 折扣券  | 8.5折        | 总量: 50     | 30%   | 启用 |
|    |             |        | 满50元可用    | 已领: 15     |       |      |
|----|-------------|--------|--------------|--------------|--------|------|
| 3  | 新人折扣券   | 折扣券  | 9折          | 总量: 200    | 5%    | 启用 |
|    |             |        | 无门槛        | 已领: 10     |       |      |
```

## 数据字段说明

### 关键字段
- `type`: 优惠券类型（1=满减券，2=折扣券）
- `minOrderAmount`: 最低消费金额（使用门槛）
- `discountValue`: 优惠值（满减券为减免金额，折扣券为折扣百分比）

### 判断逻辑
```javascript
// 判断是否有使用门槛
const hasThreshold = template.minOrderAmount && template.minOrderAmount > 0

// 折扣券显示逻辑
if (template.type === 2) {
  if (hasThreshold) {
    return `满${template.minOrderAmount}元可用`  // 有门槛
  } else {
    return '无门槛'  // 无门槛
  }
}
```

## 注意事项

1. **数据一致性**: 确保前端显示逻辑与后端业务逻辑保持一致
2. **空值处理**: 使用 `|| 0` 处理可能的空值情况
3. **类型检查**: 明确区分满减券和折扣券的显示逻辑
4. **用户体验**: "无门槛"比"满0元可用"更直观易懂
