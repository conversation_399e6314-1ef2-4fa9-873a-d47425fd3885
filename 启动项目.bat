@echo off
chcp 65001
echo ========================================
echo 书店管理系统启动脚本
echo ========================================
echo.

echo 正在检查环境状态...
echo.

REM 检查Redis是否运行
echo 检查Redis服务状态...
tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL | find /I /N "redis-server.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Redis服务正在运行
) else (
    echo ❌ Redis服务未运行，正在启动...
    start "Redis Server" cmd /c "redis-server --port 6380 --requirepass root"
    timeout /t 3 >nul
    echo ✅ Redis服务已启动
)
echo.

REM 检查MySQL服务
echo 检查MySQL服务状态...
sc query mysql | find "RUNNING" >nul
if %errorlevel% == 0 (
    echo ✅ MySQL服务正在运行
) else (
    echo ❌ MySQL服务未运行，请手动启动MySQL服务
    echo 执行命令: net start mysql
    pause
)
echo.

echo ========================================
echo 启动后端服务
echo ========================================
echo.

echo 正在启动Spring Boot后端服务...
echo 后端服务地址: http://localhost:8080
echo.

start "Bookstore Backend" cmd /k "cd /d %~dp0bookstore-server && mvn spring-boot:run"

echo 等待后端服务启动...
timeout /t 10 >nul

echo ========================================
echo 启动前端服务
echo ========================================
echo.

echo 正在启动Vue.js前端服务...
echo 前端服务地址: http://localhost:8081 (端口可能不同)
echo.

start "Bookstore Frontend" cmd /k "cd /d %~dp0bookstore-client && npm run dev"

echo.
echo ========================================
echo 启动完成！
echo ========================================
echo.
echo 服务地址:
echo 后端API: http://localhost:8080
echo 前端页面: http://localhost:8081 (请查看前端启动日志确认端口)
echo.
echo 默认测试账号:
echo 用户名: <EMAIL>
echo 密码: 123456
echo.
echo 注意事项:
echo 1. 确保MySQL数据库已创建并导入数据
echo 2. 如果端口被占用，请修改配置文件
echo 3. 首次启动可能需要较长时间
echo.

pause
