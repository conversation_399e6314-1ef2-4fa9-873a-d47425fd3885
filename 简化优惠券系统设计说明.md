# 简化优惠券系统设计说明

## 📋 需求分析

根据您的需求，优惠券系统需要支持：
- ✅ **两种优惠券类型**：满减券、折扣券
- ✅ **管理员功能**：创建并发放优惠券
- ✅ **用户功能**：领取并使用优惠券

## 🏗️ 数据库设计

### 核心表结构

#### 1. 优惠券模板表 (`coupon_template`)
**用途**：管理员创建的优惠券类型和规则

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | int(11) | 主键 | 1 |
| name | varchar(100) | 优惠券名称 | "新用户专享券" |
| description | varchar(255) | 描述 | "新用户注册即可获得20元优惠券" |
| type | tinyint(1) | 类型：1-满减，2-折扣 | 1 |
| discount_value | decimal(10,2) | 折扣值 | 20.00（满减）/ 85.00（8.5折） |
| min_order_amount | decimal(10,2) | 最低消费金额 | 100.00 |
| max_discount_amount | decimal(10,2) | 最大折扣金额（仅折扣券） | 50.00 |
| total_quantity | int(11) | 发放总数量 | 1000 |
| used_quantity | int(11) | 已使用数量 | 50 |
| received_quantity | int(11) | 已领取数量 | 200 |
| per_user_limit | int(11) | 每用户限领数量 | 1 |
| valid_days | int(11) | 有效天数 | 30 |
| status | tinyint(1) | 状态：0-停用，1-启用 | 1 |

#### 2. 用户优惠券表 (`user_coupon`)
**用途**：用户领取的优惠券实例

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | int(11) | 主键 | 1 |
| coupon_template_id | int(11) | 优惠券模板ID | 1 |
| account | varchar(100) | 用户账号 | "<EMAIL>" |
| coupon_code | varchar(50) | 优惠券码 | "FULL********001000001" |
| status | tinyint(2) | 状态：1-未使用，2-已使用，3-已过期 | 1 |
| receive_time | timestamp | 领取时间 | 2024-12-01 10:00:00 |
| use_time | timestamp | 使用时间 | NULL |
| order_id | varchar(50) | 使用的订单号 | NULL |
| expire_time | timestamp | 过期时间 | 2024-12-31 23:59:59 |
| discount_amount | decimal(10,2) | 实际折扣金额 | 0.00 |

### 修改现有表

#### 3. 订单费用表 (`expense`) - 新增字段
- `coupon_id`：使用的优惠券ID
- `coupon_discount`：优惠券折扣金额

#### 4. 订单表 (`bookorder`) - 新增字段
- `coupon_id`：使用的优惠券ID

## 💡 优惠券类型设计

### 1. 满减券 (type=1)
**规则**：订单满X元减Y元

**字段设置**：
- `discount_value`：减免金额（如20.00表示减20元）
- `min_order_amount`：最低消费金额（如100.00表示满100元）
- `max_discount_amount`：NULL（满减券不需要）

**示例**：
- 满100减20：`discount_value=20.00, min_order_amount=100.00`
- 满200减50：`discount_value=50.00, min_order_amount=200.00`

### 2. 折扣券 (type=2)
**规则**：订单享受X折优惠，最多减Y元

**字段设置**：
- `discount_value`：折扣百分比（如85.00表示8.5折）
- `min_order_amount`：最低消费金额
- `max_discount_amount`：最大折扣金额

**示例**：
- 8.5折最多减50元：`discount_value=85.00, max_discount_amount=50.00`
- 9折最多减30元：`discount_value=90.00, max_discount_amount=30.00`

## 🔄 业务流程

### 1. 管理员创建优惠券
```sql
-- 创建满减券
INSERT INTO coupon_template (name, description, type, discount_value, min_order_amount, total_quantity, per_user_limit, valid_days)
VALUES ('满100减20', '订单满100元减20元', 1, 20.00, 100.00, 1000, 1, 30);

-- 创建折扣券
INSERT INTO coupon_template (name, description, type, discount_value, min_order_amount, max_discount_amount, total_quantity, per_user_limit, valid_days)
VALUES ('8.5折券', '享受8.5折优惠，最多减50元', 2, 85.00, 50.00, 50.00, 500, 1, 15);
```

### 2. 用户领取优惠券
```sql
-- 生成优惠券码并插入用户优惠券
CALL GenerateCouponCode(1, @coupon_code);
INSERT INTO user_coupon (coupon_template_id, account, coupon_code, expire_time)
VALUES (1, '<EMAIL>', @coupon_code, DATE_ADD(NOW(), INTERVAL 30 DAY));

-- 更新模板已领取数量
UPDATE coupon_template SET received_quantity = received_quantity + 1 WHERE id = 1;
```

### 3. 用户使用优惠券
```sql
-- 验证优惠券有效性
SELECT * FROM user_coupon uc
JOIN coupon_template ct ON uc.coupon_template_id = ct.id
WHERE uc.coupon_code = 'FULL********001000001'
  AND uc.status = 1
  AND uc.expire_time > NOW()
  AND ct.status = 1;

-- 计算折扣金额（在应用层实现）
-- 满减券：IF(order_amount >= min_order_amount, discount_value, 0)
-- 折扣券：MIN(order_amount * (100 - discount_value) / 100, max_discount_amount)

-- 使用优惠券
UPDATE user_coupon 
SET status = 2, use_time = NOW(), order_id = 'ORD********0001', discount_amount = 20.00
WHERE coupon_code = 'FULL********001000001';

-- 更新模板已使用数量
UPDATE coupon_template SET used_quantity = used_quantity + 1 WHERE id = 1;
```

## 📊 查询示例

### 1. 查询用户可用优惠券
```sql
SELECT * FROM user_available_coupons WHERE account = '<EMAIL>';
```

### 2. 查询管理员优惠券统计
```sql
SELECT 
    id,
    name,
    type,
    total_quantity,
    received_quantity,
    used_quantity,
    (total_quantity - received_quantity) as remaining_quantity,
    ROUND(used_quantity * 100.0 / received_quantity, 2) as usage_rate
FROM coupon_template
WHERE status = 1;
```

### 3. 查询订单使用的优惠券
```sql
SELECT 
    bo.orderId,
    uc.coupon_code,
    ct.name as coupon_name,
    uc.discount_amount
FROM bookorder bo
LEFT JOIN user_coupon uc ON bo.coupon_id = uc.id
LEFT JOIN coupon_template ct ON uc.coupon_template_id = ct.id
WHERE bo.account = '<EMAIL>';
```

## 🎯 优惠券码规则

**格式**：`前缀 + 日期 + 模板ID + 随机数`

- **满减券**：`FULL********001000001`
  - FULL：满减券前缀
  - ********：日期
  - 001：模板ID（3位）
  - 000001：随机数（6位）

- **折扣券**：`DISC********002000001`
  - DISC：折扣券前缀
  - ********：日期
  - 002：模板ID（3位）
  - 000001：随机数（6位）

## 🔒 业务约束

1. **领取限制**：每个用户每种优惠券最多领取 `per_user_limit` 张
2. **使用限制**：每张优惠券只能使用一次
3. **时效性**：优惠券有过期时间，过期后不能使用
4. **库存控制**：优惠券总数量有限制
5. **金额限制**：满减券需要满足最低消费，折扣券有最大折扣限制

## 🚀 实施建议

1. **立即执行**：运行 `简化优惠券系统.sql` 脚本
2. **数据验证**：检查示例数据是否正确插入
3. **功能测试**：测试优惠券的创建、领取、使用流程
4. **性能优化**：根据使用情况调整索引

这个简化设计既满足了您的需求，又保持了系统的简洁性和可维护性。
