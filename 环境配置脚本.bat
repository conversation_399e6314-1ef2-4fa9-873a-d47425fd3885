@echo off
chcp 65001
echo ========================================
echo 书店管理系统环境配置脚本
echo ========================================
echo.

echo 1. 检查环境状态...
echo.

echo Java版本:
java -version
echo.

echo Node.js版本:
node -v
echo.

echo Maven版本:
mvn -v
echo.

echo MySQL版本:
mysql --version
echo.

echo Redis版本:
redis-server --version
echo.

echo ========================================
echo 2. 配置数据库
echo ========================================
echo.

echo 请按照以下步骤配置数据库：
echo.
echo 步骤1: 启动MySQL服务
echo 如果MySQL服务未启动，请执行: net start mysql
echo.

echo 步骤2: 创建数据库
echo 执行以下命令创建数据库：
echo mysql -u root -p ^< 数据库初始化脚本.sql
echo.

echo 步骤3: 创建表结构
echo mysql -u root -p bookstore ^< bookstore-server/src/main/resources/database/bookstore_ddl.sql
echo.

echo 步骤4: 插入示例数据
echo mysql -u root -p bookstore ^< bookstore-server/src/main/resources/database/bookstore_sample_data.sql
echo.

echo ========================================
echo 3. 启动Redis服务
echo ========================================
echo.
echo 请在新的命令行窗口中执行: redis-server
echo 或者启动Redis服务: net start redis
echo.

echo ========================================
echo 4. 配置后端项目
echo ========================================
echo.
echo 步骤1: 进入后端目录
echo cd bookstore-server
echo.
echo 步骤2: 安装依赖
echo mvn clean install
echo.
echo 步骤3: 启动后端服务
echo mvn spring-boot:run
echo.

echo ========================================
echo 5. 配置前端项目
echo ========================================
echo.
echo 步骤1: 进入前端目录
echo cd bookstore-client
echo.
echo 步骤2: 安装依赖
echo npm install
echo.
echo 步骤3: 启动前端服务
echo npm run dev
echo.

echo ========================================
echo 配置完成！
echo ========================================
echo.
echo 后端地址: http://localhost:8080
echo 前端地址: http://localhost:8081 (端口可能不同)
echo.

pause
