# 优惠券系统使用指南

## 📋 系统概述

本优惠券系统支持两种类型的优惠券：
- **满减券**：满X元减Y元
- **折扣券**：X折优惠，可设置最大折扣金额

## 🚀 快速开始

### 1. 数据库初始化

执行以下SQL文件来初始化优惠券系统：

```bash
# 在现有bookstore数据库中执行
mysql -u root -p bookstore < bookstore-server/src/main/resources/database/coupon_system.sql
```

### 2. 启动应用

```bash
cd bookstore-server
mvn spring-boot:run
```

## 📚 API接口文档

### 管理员接口

#### 1. 创建优惠券模板

**接口**: `POST /coupon/admin/template`

**请求体**:
```json
{
    "name": "新用户专享券",
    "description": "新用户注册即可获得20元优惠券",
    "type": 1,
    "discountValue": 20.00,
    "minOrderAmount": 100.00,
    "totalQuantity": 1000,
    "perUserLimit": 1,
    "validDays": 30
}
```

**响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "name": "新用户专享券",
        "type": 1,
        "status": 1
    }
}
```

#### 2. 查询所有优惠券模板

**接口**: `GET /coupon/admin/templates`

**响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "name": "新用户专享券",
            "typeDesc": "满减券",
            "discountDesc": "满100减20",
            "totalQuantity": 1000,
            "receivedQuantity": 50,
            "usedQuantity": 20,
            "remainingQuantity": 950,
            "usageRate": 40.00,
            "status": 1,
            "statusDesc": "启用"
        }
    ]
}
```

#### 3. 更新优惠券模板状态

**接口**: `PUT /coupon/admin/template/{id}/status?status=0`

### 用户接口

#### 1. 查询可领取的优惠券

**接口**: `GET /coupon/templates`

**响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "name": "新用户专享券",
            "description": "新用户注册即可获得20元优惠券",
            "discountDesc": "满100减20",
            "remainingQuantity": 950
        }
    ]
}
```

#### 2. 领取优惠券

**接口**: `POST /coupon/claim/{templateId}`

**响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "couponCode": "FULL20241201001000001",
        "expireTime": "2025-01-01 23:59:59"
    }
}
```

#### 3. 查询我的优惠券

**接口**: `GET /coupon/my`

**响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "couponCode": "FULL20241201001000001",
            "couponName": "新用户专享券",
            "discountDesc": "满100减20",
            "status": 1,
            "statusDesc": "未使用",
            "available": true,
            "expireTime": "2025-01-01 23:59:59"
        }
    ]
}
```

#### 4. 查询可用优惠券

**接口**: `GET /coupon/my/available`

#### 5. 计算优惠券折扣

**接口**: `POST /coupon/calculate?couponCode=FULL20241201001000001&orderAmount=150.00`

**响应**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "available": true,
        "originalAmount": 150.00,
        "discountAmount": 20.00,
        "finalAmount": 130.00,
        "couponInfo": {
            "couponName": "新用户专享券",
            "discountDesc": "满100减20"
        }
    }
}
```

#### 6. 验证优惠券

**接口**: `GET /coupon/validate?couponCode=FULL20241201001000001&orderAmount=150.00`

### 内部接口（供订单系统调用）

#### 使用优惠券

**接口**: `POST /coupon/internal/use`

**请求体**:
```json
{
    "couponCode": "FULL20241201001000001",
    "orderAmount": 150.00,
    "account": "<EMAIL>",
    "orderId": "ORDER_20241201_001"
}
```

## 🔧 业务流程

### 1. 管理员创建优惠券

1. 管理员登录后台
2. 调用创建优惠券模板接口
3. 设置优惠券参数（类型、折扣值、使用条件等）
4. 系统生成优惠券模板

### 2. 用户领取优惠券

1. 用户查看可领取的优惠券列表
2. 选择要领取的优惠券
3. 系统检查库存和用户限制
4. 生成用户优惠券实例和优惠券码
5. 设置过期时间

### 3. 用户使用优惠券

1. 用户下单时选择优惠券
2. 系统验证优惠券有效性
3. 计算折扣金额
4. 更新订单金额
5. 标记优惠券为已使用

## 💡 折扣计算规则

### 满减券
- 订单金额 >= 最低消费金额时，减免固定金额
- 例：满100减20，订单150元，减免20元，实付130元

### 折扣券
- 按百分比计算折扣：折扣金额 = 订单金额 × (100 - 折扣百分比) / 100
- 受最大折扣金额限制
- 例：8.5折最多减30元，订单300元，折扣45元但限制为30元，实付270元

## 🔍 优惠券码格式

- **满减券**: `FULL + 日期(8位) + 模板ID(3位) + 随机数(6位)`
  - 示例: `FULL20241201001000001`
  
- **折扣券**: `DISC + 日期(8位) + 模板ID(3位) + 随机数(6位)`
  - 示例: `DISC20241201002000001`

## 🛠️ 开发集成

### 在订单系统中集成优惠券

```java
// 1. 验证优惠券
@Autowired
private CouponService couponService;

public void processOrder(OrderRequest request) {
    if (request.getCouponCode() != null) {
        // 计算折扣
        CouponCalculationResult result = couponService.calculateCouponDiscount(
            request.getCouponCode(), 
            request.getAccount(), 
            request.getOrderAmount()
        );
        
        if (result.getAvailable()) {
            // 应用折扣
            BigDecimal finalAmount = result.getFinalAmount();
            
            // 使用优惠券
            CouponUsageRequest usageRequest = new CouponUsageRequest();
            usageRequest.setCouponCode(request.getCouponCode());
            usageRequest.setOrderAmount(request.getOrderAmount());
            usageRequest.setAccount(request.getAccount());
            usageRequest.setOrderId(orderId);
            
            couponService.useCoupon(usageRequest);
        }
    }
}
```

## 🧪 测试

运行测试用例：

```bash
mvn test -Dtest=CouponServiceTest
```

测试覆盖：
- ✅ 优惠券模板创建
- ✅ 用户领取优惠券
- ✅ 满减券折扣计算
- ✅ 折扣券折扣计算
- ✅ 优惠券使用
- ✅ 优惠券码生成

## 📊 数据库表结构

### coupon_template（优惠券模板表）
- 存储管理员创建的优惠券类型
- 包含折扣规则、库存信息、使用限制

### user_coupon（用户优惠券表）
- 存储用户领取的优惠券实例
- 包含优惠券码、状态、使用记录

### 扩展字段
- `expense.coupon_id`: 订单使用的优惠券ID
- `expense.coupon_discount`: 优惠券折扣金额
- `bookorder.coupon_id`: 订单关联的优惠券ID

## 🔒 安全考虑

1. **优惠券码唯一性**: 系统保证每个优惠券码全局唯一
2. **并发控制**: 使用数据库事务防止重复使用
3. **权限验证**: 用户只能使用自己的优惠券
4. **有效性检查**: 严格验证优惠券状态和过期时间

## 📈 性能优化

1. **索引优化**: 为高频查询字段添加索引
2. **缓存策略**: 可考虑缓存热门优惠券模板
3. **定时任务**: 定期清理过期优惠券

## 🚨 注意事项

1. 优惠券一旦使用不可撤销
2. 过期优惠券自动失效
3. 库存不足时无法领取
4. 每个用户的领取数量受限制
