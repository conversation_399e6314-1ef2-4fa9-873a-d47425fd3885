# 优惠券删除和停用逻辑说明

## 概述

本文档详细说明了优惠券系统中删除和停用功能的设计逻辑，确保数据安全性和业务合理性。

## 功能对比

### 删除功能 (DELETE)
**目的**: 彻底清理数据，释放数据库空间
**操作**: 从数据库中物理删除记录
**不可逆**: 删除后无法恢复

### 停用功能 (DISABLE)
**目的**: 暂停优惠券发放，但保留历史数据
**操作**: 将status字段设为0
**可逆**: 可以重新启用

## 删除条件详解

### 可以删除的情况

#### 情况1: 无人领取
```sql
SELECT COUNT(*) FROM user_coupon WHERE coupon_template_id = #{templateId}
-- 结果为 0
```
- **条件**: 没有任何用户领取过该优惠券
- **场景**: 刚创建的优惠券、测试优惠券等
- **操作**: 直接删除

#### 情况2: 所有优惠券都已使用完毕且已过期
```sql
-- 检查未使用的优惠券
SELECT COUNT(*) FROM user_coupon 
WHERE coupon_template_id = #{templateId} AND status = 1
-- 结果为 0

-- 检查未过期的优惠券
SELECT COUNT(*) FROM user_coupon 
WHERE coupon_template_id = #{templateId} AND expire_time > NOW()
-- 结果为 0
```
- **条件**: 所有已领取的优惠券都已使用且已过期
- **场景**: 历史活动结束，所有优惠券生命周期完结
- **操作**: 可以安全删除，清理历史数据

### 不能删除的情况

#### 情况1: 存在未使用的优惠券
```sql
SELECT COUNT(*) FROM user_coupon 
WHERE coupon_template_id = #{templateId} AND status = 1
-- 结果 > 0
```
- **原因**: 用户还持有未使用的优惠券
- **影响**: 删除会导致用户无法使用已领取的优惠券
- **建议**: 使用停用功能

#### 情况2: 存在未过期的优惠券
```sql
SELECT COUNT(*) FROM user_coupon 
WHERE coupon_template_id = #{templateId} AND expire_time > NOW()
-- 结果 > 0
```
- **原因**: 还有未过期的优惠券（无论是否已使用）
- **影响**: 删除会影响优惠券的查询和验证
- **建议**: 等待过期后再删除

## 停用场景

### 适用情况
1. **活动提前结束**: 需要停止发放但保留已发放的优惠券
2. **库存不足**: 暂时停止发放，后续可能恢复
3. **临时下架**: 因为某些原因需要暂停，但可能重新上架
4. **有用户持有**: 当有用户已领取优惠券时，不能删除只能停用

### 停用效果
- ✅ 停止新用户领取
- ✅ 已领取的优惠券变为不可用
- ✅ 保留所有历史数据
- ✅ 可以重新启用

## 业务流程图

```
管理员选择删除优惠券
         ↓
    检查是否有用户领取
         ↓
    没有用户领取 → 直接删除 ✅
         ↓
    有用户领取 → 检查使用状态
         ↓
    所有都已使用且过期 → 可以删除 ✅
         ↓
    存在未使用或未过期 → 拒绝删除 ❌
         ↓
    提示使用停用功能
```

## 错误信息示例

### 删除失败的错误信息
```
无法删除该优惠券模板：还有3张未使用的优惠券，还有5张未过期的优惠券。
请使用停用功能或等待所有优惠券使用完毕后再删除。
```

### 前端确认对话框
```
确定要删除优惠券"新用户专享券"吗？

删除条件：
• 没有用户领取过，或
• 所有已领取的优惠券都已使用完毕且已过期

如果不满足删除条件，请使用"停用"功能。
```

## 数据安全保障

1. **多重检查**: 检查总数、未使用数、未过期数
2. **明确提示**: 详细说明不能删除的原因
3. **替代方案**: 提供停用功能作为替代
4. **事务安全**: 删除操作在事务中执行

## 最佳实践建议

### 对于管理员
1. **新建测试优惠券**: 可以直接删除
2. **已发放的活动优惠券**: 使用停用功能
3. **历史优惠券**: 等待所有优惠券过期后删除
4. **定期清理**: 定期删除完全过期的历史优惠券

### 对于系统维护
1. **定时任务**: 可以考虑添加定时任务自动清理完全过期的优惠券
2. **数据归档**: 对于重要的历史数据，可以先归档再删除
3. **监控告警**: 监控数据库大小，及时清理冗余数据
