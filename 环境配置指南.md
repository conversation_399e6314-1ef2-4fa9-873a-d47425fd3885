# 书店管理系统环境配置指南

## 项目概述

这是一个完整的书店管理系统，包含前后端分离的架构：

- **前端**: Vue.js 2.x + Element UI + Webpack
- **后端**: Spring Boot 2.7.18 + MyBatis + MySQL + Redis + JWT认证
- **数据库**: MySQL 8.0 + Redis 6.x

## 环境要求

### 基础环境
- **操作系统**: Windows 10/11, macOS, Linux
- **Java**: JDK 17 (推荐使用OpenJDK或Oracle JDK)
- **Node.js**: 16.x 或更高版本
- **MySQL**: 8.0.x
- **Redis**: 6.x 或更高版本
- **Maven**: 3.6.x 或更高版本

## 1. 环境配置准备

### 1.1 检查已安装的环境

在命令行中执行以下命令检查环境：

```bash
# 检查Java版本
java -version
javac -version

# 检查Node.js版本
node -v
npm -v

# 检查Maven版本
mvn -v

# 检查MySQL版本
mysql --version

# 检查Redis版本
redis-server --version
```

### 1.2 安装缺失的环境

#### Java JDK 17
- 下载地址: https://adoptium.net/
- 安装后配置环境变量 `JAVA_HOME`

#### Node.js
- 下载地址: https://nodejs.org/
- 推荐安装LTS版本

#### Maven
- 下载地址: https://maven.apache.org/download.cgi
- 配置环境变量 `MAVEN_HOME`

#### MySQL 8.0
- 下载地址: https://dev.mysql.com/downloads/mysql/
- 安装时记住root密码

#### Redis
- Windows: https://github.com/microsoftarchive/redis/releases
- macOS: `brew install redis`
- Linux: `sudo apt-get install redis-server`

## 2. 数据库环境配置

### 2.1 MySQL配置

1. **启动MySQL服务**
```bash
# Windows
net start mysql

# macOS/Linux
sudo systemctl start mysql
```

2. **创建数据库**
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE bookstore CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'bookstore'@'localhost' IDENTIFIED BY 'bookstore123';
GRANT ALL PRIVILEGES ON bookstore.* TO 'bookstore'@'localhost';
FLUSH PRIVILEGES;
```

3. **执行数据库脚本**
```bash
# 进入项目目录
cd bookstore-server

# 执行DDL脚本（创建表结构）
mysql -u root -p bookstore < src/main/resources/database/bookstore_ddl.sql

# 执行示例数据脚本
mysql -u root -p bookstore < src/main/resources/database/bookstore_sample_data.sql
```

### 2.2 Redis配置

1. **启动Redis服务**
```bash
# Windows
redis-server

# macOS/Linux
redis-server /etc/redis/redis.conf
```

2. **修改Redis配置（如需要）**
- 默认端口: 6379
- 如果需要密码认证，修改配置文件中的 `requirepass`

## 3. 后端环境配置

### 3.1 修改数据库配置

编辑 `bookstore-server/src/main/resources/application-dev.yml`：

```yaml
spring:
  shardingsphere:
    datasource:
      master:
        url: *************************************************************
        username: root
        password: 你的MySQL密码
      slave:
        url: *************************************************************
        username: root
        password: 你的MySQL密码
  redis:
    host: 127.0.0.1
    port: 6379
    password: 你的Redis密码（如果设置了）
```

### 3.2 安装后端依赖

```bash
cd bookstore-server

# 清理并安装依赖
mvn clean install

# 或者只下载依赖
mvn dependency:resolve
```

## 4. 前端环境配置

### 4.1 安装前端依赖

```bash
cd bookstore-client

# 安装依赖
npm install

# 如果npm安装慢，可以使用cnpm
npm install -g cnpm --registry=https://registry.npm.taobao.org
cnpm install
```

### 4.2 配置前端API地址

检查前端配置文件，确保API地址正确指向后端服务。

## 5. 项目启动

### 5.1 启动后端服务

```bash
cd bookstore-server

# 方式1: 使用Maven
mvn spring-boot:run

# 方式2: 使用Java命令
mvn clean package
java -jar target/store-0.0.1-SNAPSHOT.jar
```

后端服务将在 http://localhost:8080 启动

### 5.2 启动前端服务

```bash
cd bookstore-client

# 启动开发服务器
npm run dev
```

前端服务将在 http://localhost:8081 启动（端口可能不同）

## 6. 验证安装

### 6.1 检查服务状态

1. **后端服务检查**
   - 访问: http://localhost:8080
   - 检查控制台日志是否有错误

2. **前端服务检查**
   - 访问前端地址
   - 检查是否能正常加载页面

3. **数据库连接检查**
   - 查看后端日志中的数据库连接信息
   - 确认没有连接错误

### 6.2 功能测试

1. **用户注册/登录**
2. **图书浏览**
3. **购物车功能**
4. **订单管理**

## 常见问题解决

### 问题1: 端口占用
```bash
# 查看端口占用
netstat -ano | findstr :8080
netstat -ano | findstr :8081

# 杀死占用端口的进程
taskkill /PID <进程ID> /F
```

### 问题2: MySQL连接失败
- 检查MySQL服务是否启动
- 确认用户名密码正确
- 检查防火墙设置

### 问题3: Redis连接失败
- 检查Redis服务是否启动
- 确认端口和密码配置

### 问题4: 前端依赖安装失败
```bash
# 清理缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules
npm install
```

## 开发工具推荐

- **IDE**: IntelliJ IDEA (后端) + VS Code (前端)
- **数据库管理**: MySQL Workbench, Navicat
- **API测试**: Postman, Apifox
- **版本控制**: Git

## 下一步

环境配置完成后，您可以：
1. 熟悉项目结构和代码
2. 查看API文档
3. 进行功能开发和测试
4. 部署到生产环境

如有问题，请参考项目文档或联系技术支持。
