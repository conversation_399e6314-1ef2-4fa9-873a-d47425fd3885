Index: src/main/resources/database/bookstore_ddl.sql
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>-- ========================================\r\n-- 书店系统数据库初始化脚本\r\n-- 数据库名称: bookstore\r\n-- 字符集: utf8mb4\r\n-- 排序规则: utf8mb4_unicode_ci\r\n-- ========================================\r\nDROP DATABASE IF EXISTS `bookstore`;\r\n-- 创建数据库\r\nCREATE DATABASE IF NOT EXISTS `bookstore`\r\n    DEFAULT CHARACTER SET utf8mb4\r\n    COLLATE utf8mb4_unicode_ci;\r\n\r\nUSE `bookstore`;\r\n\r\n-- ========================================\r\n-- 用户相关表\r\n-- ========================================\r\n\r\n-- 用户表\r\nCREATE TABLE `user` (\r\n                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户编号',\r\n                        `account` varchar(100) NOT NULL COMMENT '用户账号(邮箱)',\r\n                        `password` varchar(255) NOT NULL COMMENT '密码',\r\n                        `name` varchar(50) DEFAULT NULL COMMENT '用户姓名',\r\n                        `gender` varchar(10) DEFAULT NULL COMMENT '性别',\r\n                        `imgUrl` varchar(255) DEFAULT NULL COMMENT '头像URL',\r\n                        `info` text COMMENT '个人简介',\r\n                        `manage` tinyint(1) DEFAULT '0' COMMENT '是否为管理员',\r\n                        `enable` tinyint(1) DEFAULT '1' COMMENT '是否启用',\r\n                        `registerTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',\r\n                        PRIMARY KEY (`id`),\r\n                        UNIQUE KEY `uk_account` (`account`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';\r\n\r\n-- 地址表\r\nCREATE TABLE `address` (\r\n                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '地址编号',\r\n                           `account` varchar(100) NOT NULL COMMENT '用户账号',\r\n                           `name` varchar(50) NOT NULL COMMENT '收货人姓名',\r\n                           `phone` varchar(20) NOT NULL COMMENT '收货人电话',\r\n                           `addr` varchar(255) NOT NULL COMMENT '具体地址',\r\n                           `label` varchar(50) DEFAULT NULL COMMENT '地址标签',\r\n                           `off` tinyint(1) DEFAULT '0' COMMENT '是否删除',\r\n                           PRIMARY KEY (`id`),\r\n                           KEY `idx_account` (`account`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收货地址表';\r\n\r\n-- 购物车表\r\nCREATE TABLE `cart` (\r\n                        `account` varchar(100) NOT NULL COMMENT '用户账号',\r\n                        `id` int(11) NOT NULL COMMENT '图书ID',\r\n                        `num` int(11) DEFAULT '1' COMMENT '数量',\r\n                        `addTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',\r\n                        PRIMARY KEY (`account`, `id`),\r\n                        KEY `idx_account` (`account`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物车表';\r\n\r\n-- ========================================\r\n-- 图书相关表\r\n-- ========================================\r\n\r\n-- 图书表\r\nCREATE TABLE `book` (\r\n                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '图书编号',\r\n                        `bookName` varchar(255) NOT NULL COMMENT '图书名称',\r\n                        `author` varchar(255) DEFAULT NULL COMMENT '作者',\r\n                        `isbn` varchar(50) NOT NULL COMMENT 'ISBN号',\r\n                        `publish` varchar(255) DEFAULT NULL COMMENT '出版社',\r\n                        `birthday` timestamp NULL DEFAULT NULL COMMENT '出版时间',\r\n                        `marketPrice` decimal(10,2) DEFAULT NULL COMMENT '市场价',\r\n                        `price` decimal(10,2) DEFAULT NULL COMMENT '售价',\r\n                        `stock` int(11) DEFAULT '0' COMMENT '库存',\r\n                        `description` text COMMENT '图书描述',\r\n                        `put` tinyint(1) DEFAULT '1' COMMENT '是否上架',\r\n                        `rank` int(11) DEFAULT '0' COMMENT '权重值',\r\n                        `newProduct` tinyint(1) DEFAULT '0' COMMENT '是否新品',\r\n                        `recommend` tinyint(1) DEFAULT '0' COMMENT '是否推荐',\r\n                        PRIMARY KEY (`id`),\r\n                        UNIQUE KEY `uk_isbn` (`isbn`),\r\n                        KEY `idx_publish` (`publish`),\r\n                        KEY `idx_put` (`put`),\r\n                        KEY `idx_newProduct` (`newProduct`),\r\n                        KEY `idx_recommend` (`recommend`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书表';\r\n\r\n-- 图书图片表\r\nCREATE TABLE `bookimg` (\r\n                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '图片编号',\r\n                           `isbn` varchar(50) NOT NULL COMMENT '图书ISBN',\r\n                           `imgSrc` varchar(255) NOT NULL COMMENT '图片路径',\r\n                           `cover` tinyint(1) DEFAULT '0' COMMENT '是否为封面',\r\n                           PRIMARY KEY (`id`),\r\n                           KEY `idx_isbn` (`isbn`),\r\n                           KEY `idx_cover` (`cover`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书图片表';\r\n\r\n-- 图书分类表\r\nCREATE TABLE `booksort` (\r\n                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类编号',\r\n                            `sortName` varchar(100) NOT NULL COMMENT '分类名称',\r\n                            `upperName` varchar(100) DEFAULT '无' COMMENT '上级分类名称',\r\n                            `level` varchar(20) DEFAULT NULL COMMENT '分类级别',\r\n                            `rank` int(11) DEFAULT '0' COMMENT '排序权重',\r\n                            PRIMARY KEY (`id`),\r\n                            KEY `idx_upperName` (`upperName`),\r\n                            KEY `idx_level` (`level`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书分类表';\r\n\r\n-- 图书分类关联表\r\nCREATE TABLE `booksortlist` (\r\n                                `bookSortId` int(11) NOT NULL COMMENT '分类ID',\r\n                                `bookId` int(11) NOT NULL COMMENT '图书ID',\r\n                                PRIMARY KEY (`bookSortId`, `bookId`),\r\n                                KEY `idx_bookId` (`bookId`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书分类关联表';\r\n\r\n-- 出版社表\r\nCREATE TABLE `publish` (\r\n                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '出版社编号',\r\n                           `name` varchar(255) NOT NULL COMMENT '出版社名称',\r\n                           `showPublish` tinyint(1) DEFAULT '1' COMMENT '是否显示',\r\n                           `rank` int(11) DEFAULT '0' COMMENT '排序权重',\r\n                           `num` int(11) DEFAULT '0' COMMENT '图书数量',\r\n                           PRIMARY KEY (`id`),\r\n                           UNIQUE KEY `uk_name` (`name`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出版社表';\r\n\r\n-- ========================================\r\n-- 书单相关表\r\n-- ========================================\r\n\r\n-- 书单主题表\r\nCREATE TABLE `booktopic` (\r\n                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '书单编号',\r\n                             `topicName` varchar(255) NOT NULL COMMENT '书单名称',\r\n                             `subTitle` varchar(255) DEFAULT NULL COMMENT '副标题',\r\n                             `cover` varchar(255) DEFAULT NULL COMMENT '封面图片',\r\n                             `rank` int(11) DEFAULT '0' COMMENT '排序权重',\r\n                             `put` tinyint(1) DEFAULT '1' COMMENT '是否上架',\r\n                             PRIMARY KEY (`id`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='书单主题表';\r\n\r\n-- 书单图书关联表\r\nCREATE TABLE `subbooktopic` (\r\n                                `topicId` int(11) NOT NULL COMMENT '书单ID',\r\n                                `bookId` int(11) NOT NULL COMMENT '图书ID',\r\n                                `recommendReason` text COMMENT '推荐理由',\r\n                                PRIMARY KEY (`topicId`, `bookId`),\r\n                                KEY `idx_bookId` (`bookId`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='书单图书关联表';\r\n\r\n-- ========================================\r\n-- 订单相关表\r\n-- ========================================\r\n\r\n-- 订单表\r\nCREATE TABLE `bookorder` (\r\n                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单编号',\r\n                             `orderId` varchar(50) NOT NULL COMMENT '订单号',\r\n                             `account` varchar(100) NOT NULL COMMENT '用户账号',\r\n                             `addressId` int(11) NOT NULL COMMENT '收货地址ID',\r\n                             `orderTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',\r\n                             `shipTime` timestamp NULL DEFAULT NULL COMMENT '发货时间',\r\n                             `getTime` timestamp NULL DEFAULT NULL COMMENT '收货时间',\r\n                             `evaluateTime` timestamp NULL DEFAULT NULL COMMENT '评价时间',\r\n                             `closeTime` timestamp NULL DEFAULT NULL COMMENT '关闭时间',\r\n                             `confirmTime` timestamp NULL DEFAULT NULL COMMENT '确认收货时间',\r\n                             `orderStatus` varchar(50) DEFAULT '待付款' COMMENT '订单状态',\r\n                             `logisticsCompany` int(11) DEFAULT NULL COMMENT '物流公司ID',\r\n                             `logisticsNum` varchar(100) DEFAULT NULL COMMENT '物流单号',\r\n                             `beUserDelete` tinyint(1) DEFAULT '0' COMMENT '用户是否删除',\r\n                             PRIMARY KEY (`id`),\r\n                             UNIQUE KEY `uk_orderId` (`orderId`),\r\n                             KEY `idx_account` (`account`),\r\n                             KEY `idx_orderStatus` (`orderStatus`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';\r\n\r\n-- 订单明细表\r\nCREATE TABLE `orderdetail` (\r\n                               `orderId` varchar(50) NOT NULL COMMENT '订单号',\r\n                               `bookId` int(11) NOT NULL COMMENT '图书ID',\r\n                               `num` int(11) NOT NULL COMMENT '购买数量',\r\n                               `price` decimal(10,2) NOT NULL COMMENT '购买时单价',\r\n                               PRIMARY KEY (`orderId`, `bookId`),\r\n                               KEY `idx_bookId` (`bookId`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';\r\n\r\n-- 订单费用表\r\nCREATE TABLE `expense` (\r\n                           `orderId` varchar(50) NOT NULL COMMENT '订单号',\r\n                           `productTotalMoney` decimal(10,2) DEFAULT '0.00' COMMENT '商品总价',\r\n                           `freight` decimal(10,2) DEFAULT '0.00' COMMENT '运费',\r\n                           `coupon` int(11) DEFAULT '0' COMMENT '优惠券',\r\n                           `activityDiscount` decimal(10,2) DEFAULT '0.00' COMMENT '活动优惠',\r\n                           `allPrice` decimal(10,2) DEFAULT '0.00' COMMENT '订单总金额',\r\n                           `finallyPrice` decimal(10,2) DEFAULT '0.00' COMMENT '最终实付金额',\r\n                           PRIMARY KEY (`orderId`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单费用表';\r\n\r\n-- ========================================\r\n-- 创建缺失的数据库表\r\n-- ========================================\r\n\r\nUSE `bookstore`;\r\n\r\n-- 新品推荐表\r\nCREATE TABLE `newproduct` (\r\n                              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '新品推荐编号',\r\n                              `bookId` int(11) NOT NULL COMMENT '图书ID',\r\n                              `rank` int(11) DEFAULT '0' COMMENT '推荐权重',\r\n                              `addTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',\r\n                              PRIMARY KEY (`id`),\r\n                              UNIQUE KEY `uk_bookId` (`bookId`),\r\n                              KEY `idx_rank` (`rank`),\r\n                              KEY `idx_addTime` (`addTime`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新品推荐表';\r\n\r\n-- 推荐图书表\r\nCREATE TABLE `recommend` (\r\n                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '推荐编号',\r\n                             `bookId` int(11) NOT NULL COMMENT '图书ID',\r\n                             `rank` int(11) DEFAULT '0' COMMENT '推荐权重',\r\n                             `addTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',\r\n                             PRIMARY KEY (`id`),\r\n                             UNIQUE KEY `uk_bookId` (`bookId`),\r\n                             KEY `idx_rank` (`rank`),\r\n                             KEY `idx_addTime` (`addTime`)\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推荐图书表';\r\n\r\n-- 添加外键约束\r\nALTER TABLE `newproduct` ADD CONSTRAINT `fk_newproduct_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`) ON DELETE CASCADE;\r\nALTER TABLE `recommend` ADD CONSTRAINT `fk_recommend_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`) ON DELETE CASCADE;\r\n\r\n-- 插入一些示例数据（如果book表中有数据的话）\r\n-- 注意：这些INSERT语句只有在book表中存在对应ID的记录时才会成功\r\n\r\n-- 检查是否有图书数据\r\nSELECT COUNT(*) as book_count FROM book;\r\n\r\n-- 如果有图书数据，可以添加一些推荐和新品\r\n-- INSERT INTO `newproduct` (`bookId`, `rank`)\r\n-- SELECT `id`, 1 FROM `book` WHERE `newProduct` = 1 LIMIT 10;\r\n\r\n-- INSERT INTO `recommend` (`bookId`, `rank`)\r\n-- SELECT `id`, 1 FROM `book` WHERE `recommend` = 1 LIMIT 10;\r\n\r\n\r\n-- ========================================\r\n-- 索引和外键约束\r\n-- ========================================\r\n\r\n-- 添加外键约束\r\nALTER TABLE `address` ADD CONSTRAINT `fk_address_user` FOREIGN KEY (`account`) REFERENCES `user` (`account`) ON DELETE CASCADE;\r\nALTER TABLE `cart` ADD CONSTRAINT `fk_cart_user` FOREIGN KEY (`account`) REFERENCES `user` (`account`) ON DELETE CASCADE;\r\nALTER TABLE `cart` ADD CONSTRAINT `fk_cart_book` FOREIGN KEY (`id`) REFERENCES `book` (`id`) ON DELETE CASCADE;\r\nALTER TABLE `bookimg` ADD CONSTRAINT `fk_bookimg_book` FOREIGN KEY (`isbn`) REFERENCES `book` (`isbn`) ON DELETE CASCADE;\r\nALTER TABLE `booksortlist` ADD CONSTRAINT `fk_booksortlist_sort` FOREIGN KEY (`bookSortId`) REFERENCES `booksort` (`id`) ON DELETE CASCADE;\r\nALTER TABLE `booksortlist` ADD CONSTRAINT `fk_booksortlist_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`) ON DELETE CASCADE;\r\nALTER TABLE `subbooktopic` ADD CONSTRAINT `fk_subbooktopic_topic` FOREIGN KEY (`topicId`) REFERENCES `booktopic` (`id`) ON DELETE CASCADE;\r\nALTER TABLE `subbooktopic` ADD CONSTRAINT `fk_subbooktopic_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`) ON DELETE CASCADE;\r\nALTER TABLE `bookorder` ADD CONSTRAINT `fk_bookorder_user` FOREIGN KEY (`account`) REFERENCES `user` (`account`) ON DELETE CASCADE;\r\nALTER TABLE `bookorder` ADD CONSTRAINT `fk_bookorder_address` FOREIGN KEY (`addressId`) REFERENCES `address` (`id`);\r\nALTER TABLE `orderdetail` ADD CONSTRAINT `fk_orderdetail_order` FOREIGN KEY (`orderId`) REFERENCES `bookorder` (`orderId`) ON DELETE CASCADE;\r\nALTER TABLE `orderdetail` ADD CONSTRAINT `fk_orderdetail_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`);\r\nALTER TABLE `expense` ADD CONSTRAINT `fk_expense_order` FOREIGN KEY (`orderId`) REFERENCES `bookorder` (`orderId`) ON DELETE CASCADE;\r\n\r\n\r\n-- ========================================\r\n-- 检查和添加基础数据\r\n-- ========================================\r\n\r\n-- ========================================\r\n-- 书店系统示例数据插入脚本\r\n-- 注意：请先执行 bookstore_ddl.sql 创建表结构\r\n-- ========================================\r\n\r\nUSE `bookstore`;\r\n\r\n-- ========================================\r\n-- 用户数据\r\n-- ========================================\r\n\r\n-- 插入管理员用户\r\nINSERT INTO `user` (`account`, `password`, `name`, `gender`, `manage`, `enable`) VALUES\r\n                                                                                     ('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '管理员', '男', 1, 1),\r\n                                                                                     ('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '张三', '男', 0, 1),\r\n                                                                                     ('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '李四', '女', 0, 1);\r\n\r\n-- 插入地址数据\r\nINSERT INTO `address` (`account`, `name`, `phone`, `addr`, `label`) VALUES\r\n                                                                        ('<EMAIL>', '张三', '***********', '北京市朝阳区某某街道123号', '家'),\r\n                                                                        ('<EMAIL>', '张三', '***********', '北京市海淀区某某大厦456号', '公司'),\r\n                                                                        ('<EMAIL>', '李四', '***********', '上海市浦东新区某某路789号', '家');\r\n\r\n-- ========================================\r\n-- 图书分类数据\r\n-- ========================================\r\n\r\n-- 一级分类\r\nINSERT INTO `booksort` (`sortName`, `upperName`, `level`, `rank`) VALUES\r\n                                                                      ('文学', '无', '级别一', 1),\r\n                                                                      ('科技', '无', '级别一', 2),\r\n                                                                      ('教育', '无', '级别一', 3),\r\n                                                                      ('生活', '无', '级别一', 4);\r\n\r\n-- 二级分类\r\nINSERT INTO `booksort` (`sortName`, `upperName`, `level`, `rank`) VALUES\r\n                                                                      ('小说', '文学', '级别二', 1),\r\n                                                                      ('散文', '文学', '级别二', 2),\r\n                                                                      ('诗歌', '文学', '级别二', 3),\r\n                                                                      ('计算机', '科技', '级别二', 1),\r\n                                                                      ('电子', '科技', '级别二', 2),\r\n                                                                      ('教材', '教育', '级别二', 1),\r\n                                                                      ('考试', '教育', '级别二', 2),\r\n                                                                      ('健康', '生活', '级别二', 1),\r\n                                                                      ('美食', '生活', '级别二', 2);\r\n\r\n-- ========================================\r\n-- 出版社数据\r\n-- ========================================\r\n\r\nINSERT INTO `publish` (`name`, `showPublish`, `rank`) VALUES\r\n                                                          ('人民文学出版社', 1, 1),\r\n                                                          ('机械工业出版社', 1, 2),\r\n                                                          ('清华大学出版社', 1, 3),\r\n                                                          ('电子工业出版社', 1, 4),\r\n                                                          ('中信出版社', 1, 5);\r\n\r\n-- ========================================\r\n-- 图书数据\r\n-- ========================================\r\n\r\nINSERT INTO `book` (`bookName`, `author`, `isbn`, `publish`, `birthday`, `marketPrice`, `price`, `stock`, `description`, `put`, `rank`, `newProduct`, `recommend`) VALUES\r\n                                                                                                                                                                       ('红楼梦', '曹雪芹', '9787020002207', '人民文学出版社', '2020-01-01 00:00:00', 59.00, 45.00, 100, '中国古典文学四大名著之一', 1, 10, 0, 1),\r\n                                                                                                                                                                       ('Java核心技术', 'Cay S. Horstmann', '9787111213826', '机械工业出版社', '2021-03-15 00:00:00', 128.00, 98.00, 50, 'Java编程经典教程', 1, 9, 1, 1),\r\n                                                                                                                                                                       ('算法导论', 'Thomas H. Cormen', '9787111407010', '机械工业出版社', '2020-06-01 00:00:00', 158.00, 128.00, 30, '计算机算法经典教材', 1, 8, 0, 1),\r\n                                                                                                                                                                       ('Spring Boot实战', '汪云飞', '9787121291005', '电子工业出版社', '2021-05-20 00:00:00', 89.00, 69.00, 80, 'Spring Boot开发实战指南', 1, 7, 1, 0),\r\n                                                                                                                                                                       ('西游记', '吴承恩', '9787020002214', '人民文学出版社', '2020-02-01 00:00:00', 49.00, 38.00, 120, '中国古典文学四大名著之一', 1, 6, 0, 1);\r\n\r\n-- ========================================\r\n-- 图书图片数据\r\n-- ========================================\r\n\r\nINSERT INTO `bookimg` (`isbn`, `imgSrc`, `cover`) VALUES\r\n                                                      ('9787020002207', 'static/image/book/hongloumeng_cover.jpg', 1),\r\n                                                      ('9787020002207', 'static/image/book/hongloumeng_1.jpg', 0),\r\n                                                      ('9787111213826', 'static/image/book/java_cover.jpg', 1),\r\n                                                      ('9787111213826', 'static/image/book/java_1.jpg', 0),\r\n                                                      ('9787111407010', 'static/image/book/algorithm_cover.jpg', 1),\r\n                                                      ('9787121291005', 'static/image/book/springboot_cover.jpg', 1),\r\n                                                      ('9787020002214', 'static/image/book/xiyouji_cover.jpg', 1);\r\n\r\n-- ========================================\r\n-- 图书分类关联数据\r\n-- ========================================\r\n\r\nINSERT INTO `booksortlist` (`bookSortId`, `bookId`) VALUES\r\n                                                        (5, 1),  -- 红楼梦 -> 小说\r\n                                                        (5, 5),  -- 西游记 -> 小说\r\n                                                        (8, 2),  -- Java核心技术 -> 计算机\r\n                                                        (8, 3),  -- 算法导论 -> 计算机\r\n                                                        (8, 4);  -- Spring Boot实战 -> 计算机\r\n\r\n-- ========================================\r\n-- 书单数据\r\n-- ========================================\r\n\r\nINSERT INTO `booktopic` (`topicName`, `subTitle`, `cover`, `rank`, `put`) VALUES\r\n                                                                              ('程序员必读书单', '提升编程技能的经典书籍', 'static/image/topic/programmer_books.jpg', 1, 1),\r\n                                                                              ('古典文学精选', '传承千年的文学瑰宝', 'static/image/topic/classic_literature.jpg', 2, 1);\r\n\r\nINSERT INTO `subbooktopic` (`topicId`, `bookId`, `recommendReason`) VALUES\r\n                                                                        (1, 2, 'Java开发者的必备参考书，内容全面深入'),\r\n                                                                        (1, 3, '算法学习的经典教材，计算机科学基础'),\r\n                                                                        (1, 4, '现代Java开发框架实战指南'),\r\n                                                                        (2, 1, '中国古典小说的巅峰之作，文学价值极高'),\r\n                                                                        (2, 5, '神话色彩浓厚的古典小说，想象力丰富');\r\n\r\n-- ========================================\r\n-- 购物车示例数据\r\n-- ========================================\r\n\r\nINSERT INTO `cart` (`account`, `id`, `num`) VALUES\r\n                                                ('<EMAIL>', 1, 2),\r\n                                                ('<EMAIL>', 2, 1),\r\n                                                ('<EMAIL>', 3, 1);\r\n\r\n-- ========================================\r\n-- 订单示例数据\r\n-- ========================================\r\n\r\nINSERT INTO `bookorder` (`orderId`, `account`, `addressId`, `orderTime`, `orderStatus`) VALUES\r\n                                                                                            ('ORD202401010001', '<EMAIL>', 1, '2024-01-01 10:30:00', '已完成'),\r\n                                                                                            ('ORD202401020001', '<EMAIL>', 3, '2024-01-02 14:20:00', '待发货');\r\n\r\nINSERT INTO `orderdetail` (`orderId`, `bookId`, `num`, `price`) VALUES\r\n                                                                    ('ORD202401010001', 1, 1, 45.00),\r\n                                                                    ('ORD202401010001', 2, 1, 98.00),\r\n                                                                    ('ORD202401020001', 3, 1, 128.00);\r\n\r\nINSERT INTO `expense` (`orderId`, `productTotalMoney`, `freight`, `coupon`, `activityDiscount`, `allPrice`, `finallyPrice`) VALUES\r\n                                                                                                                                ('ORD202401010001', 143.00, 0.00, 0, 0.00, 143.00, 143.00),\r\n                                                                                                                                ('ORD202401020001', 128.00, 0.00, 0, 0.00, 128.00, 128.00);\r\n\r\n-- ========================================\r\n-- 更新出版社图书数量\r\n-- ========================================\r\n\r\nUPDATE `publish` SET `num` = (\r\n    SELECT COUNT(*) FROM `book` WHERE `book`.`publish` = `publish`.`name`\r\n) WHERE `id` > 0;\r\n\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/resources/database/bookstore_ddl.sql b/src/main/resources/database/bookstore_ddl.sql
--- a/src/main/resources/database/bookstore_ddl.sql	(revision 2fe4bb9fb49dfb4c3023484231491c68cd9961e2)
+++ b/src/main/resources/database/bookstore_ddl.sql	(date 1751332688704)
@@ -98,7 +98,7 @@
 CREATE TABLE `booksort` (
                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类编号',
                             `sortName` varchar(100) NOT NULL COMMENT '分类名称',
-                            `upperName` varchar(100) DEFAULT '无' COMMENT '上级分类名称',
+                            `upperName` varchar(100) DEFAULT NULL COMMENT '上级分类名称',
                             `level` varchar(20) DEFAULT NULL COMMENT '分类级别',
                             `rank` int(11) DEFAULT '0' COMMENT '排序权重',
                             PRIMARY KEY (`id`),
@@ -165,7 +165,7 @@
                              `evaluateTime` timestamp NULL DEFAULT NULL COMMENT '评价时间',
                              `closeTime` timestamp NULL DEFAULT NULL COMMENT '关闭时间',
                              `confirmTime` timestamp NULL DEFAULT NULL COMMENT '确认收货时间',
-                             `orderStatus` varchar(50) DEFAULT '待付款' COMMENT '订单状态',
+                             `orderStatus` varchar(50) DEFAULT NULL COMMENT '订单状态',
                              `logisticsCompany` int(11) DEFAULT NULL COMMENT '物流公司ID',
                              `logisticsNum` varchar(100) DEFAULT NULL COMMENT '物流单号',
                              `beUserDelete` tinyint(1) DEFAULT '0' COMMENT '用户是否删除',
Index: src/main/resources/application-dev.yml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>server:\r\n  port: 8080\r\n\r\nspring:\r\n  main:\r\n    allow-bean-definition-overriding: true\r\n    allow-circular-references: true\r\n  shardingsphere:\r\n    datasource:\r\n      names:\r\n        master,slave\r\n      # 主数据源\r\n      master:\r\n        driver-class-name: com.mysql.cj.jdbc.Driver\r\n        url: *****************************************************************        username: root\r\n        password: root\r\n        # 使用druid数据源\r\n        type: com.alibaba.druid.pool.DruidDataSource\r\n        # 配置获取连接等待超时的时间\r\n        # 下面为连接池的补充设置，应用到上面所有数据源中\r\n        # 初始化大小，最小，最大\r\n        initialSize: 1\r\n        minIdle: 3\r\n        maxActive: 20\r\n        # 配置获取连接等待超时的时间\r\n        maxWait: 60000\r\n        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒\r\n        timeBetweenEvictionRunsMillis: 60000\r\n        # 配置一个连接在池中最小生存的时间，单位是毫秒\r\n        minEvictableIdleTimeMillis: 30000\r\n        validationQuery: select 'x'\r\n        testWhileIdle: true\r\n        testOnBorrow: false\r\n        testOnReturn: false\r\n        # 打开PSCache，并且指定每个连接上PSCache的大小\r\n        poolPreparedStatements: true\r\n        maxPoolPreparedStatementPerConnectionSize: 20\r\n        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙\r\n        filters: stat,wall,slf4j\r\n        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录\r\n        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\r\n        # 合并多个DruidDataSource的监控数据\r\n        useGlobalDataSourceStat: true\r\n      # 从数据源\r\n      slave:\r\n        driver-class-name: com.mysql.cj.jdbc.Driver\r\n        url: *****************************************************************        username: root\r\n        password: root\r\n        # 使用druid数据源\r\n        type: com.alibaba.druid.pool.DruidDataSource\r\n        # 配置获取连接等待超时的时间\r\n        # 下面为连接池的补充设置，应用到上面所有数据源中\r\n        # 初始化大小，最小，最大\r\n        initialSize: 1\r\n        minIdle: 3\r\n        maxActive: 20\r\n        # 配置获取连接等待超时的时间\r\n        maxWait: 60000\r\n        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒\r\n        timeBetweenEvictionRunsMillis: 60000\r\n        # 配置一个连接在池中最小生存的时间，单位是毫秒\r\n        minEvictableIdleTimeMillis: 30000\r\n        validationQuery: select 'x'\r\n        testWhileIdle: true\r\n        testOnBorrow: false\r\n        testOnReturn: false\r\n        # 打开PSCache，并且指定每个连接上PSCache的大小\r\n        poolPreparedStatements: true\r\n        maxPoolPreparedStatementPerConnectionSize: 20\r\n        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙\r\n        filters: stat,wall,slf4j\r\n        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录\r\n        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000\r\n        # 合并多个DruidDataSource的监控数据\r\n        useGlobalDataSourceStat: true\r\n    masterslave:\r\n      # 读写分离配置\r\n      load-balance-algorithm-type: round_robin\r\n      # 最终的数据源名称\r\n      name: dataSource\r\n      # 主库数据源名称\r\n      master-data-source-name: master\r\n      # 从库数据源名称列表，多个逗号分隔\r\n      slave-data-source-names: slave\r\n    props:\r\n      # 开启SQL显示，默认false\r\n      sql:\r\n        show: true\r\n  ## Redis 配置\r\n  redis:\r\n    ## Redis数据库索引（默认为0）\r\n    database: 0\r\n    ## Redis服务器地址\r\n    host: 127.0.0.1\r\n    ## Redis服务器连接端口\r\n    port: 6380\r\n    ## Redis服务器连接密码（默认为空）\r\n    password: root\r\n    jedis:\r\n      pool:\r\n        ## 连接池最大连接数（使用负值表示没有限制）\r\n        #spring.redis.pool.max-active=8\r\n        max-active: 8\r\n        ## 连接池最大阻塞等待时间（使用负值表示没有限制）\r\n        #spring.redis.pool.max-wait=-1\r\n        max-wait: -1\r\n        ## 连接池中的最大空闲连接\r\n        #spring.redis.pool.max-idle=8\r\n        max-idle: 8\r\n        ## 连接池中的最小空闲连接\r\n        #spring.redis.pool.min-idle=0\r\n        min-idle: 0\r\n    ## 连接超时时间（毫秒）\r\n    timeout: 1200\r\n\r\n  # 配置SpringMVC文件上传限制，默认1M。注意MB要大写，不然报错。SpringBoot版本不同，配置项不同\r\n  servlet:\r\n    multipart:\r\n      max-file-size: 50MB\r\n      max-request-size: 50MB\r\n  devtools:\r\n    restart:\r\n      log-condition-evaluation-delta: false\r\n    livereload:\r\n      port: 35730\r\n  thymeleaf:\r\n    cache: false\r\n\r\n\r\nmybatis:\r\n  mapper-locations: classpath:mapping/*Mapper.xml\r\n  type-aliases-package: com.huang.store.entity\r\n\r\n# 文件上传配置\r\nfile:\r\n  upload:\r\n    # 基础路径 - 现在指向服务端resources下的static文件夹\r\n    base-path: classpath:static/\r\n    # 图书图片相对路径\r\n    book-path: image/book/\r\n    # 书单封面相对路径\r\n    topic-path: image/topic/\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/resources/application-dev.yml b/src/main/resources/application-dev.yml
--- a/src/main/resources/application-dev.yml	(revision 2fe4bb9fb49dfb4c3023484231491c68cd9961e2)
+++ b/src/main/resources/application-dev.yml	(date 1751332567029)
@@ -14,7 +14,7 @@
         driver-class-name: com.mysql.cj.jdbc.Driver
         url: *************************************************************
         username: root
-        password: root
+        password:
         # 使用druid数据源
         type: com.alibaba.druid.pool.DruidDataSource
         # 配置获取连接等待超时的时间
@@ -47,7 +47,7 @@
         driver-class-name: com.mysql.cj.jdbc.Driver
         url: *************************************************************
         username: root
-        password: root
+        password:
         # 使用druid数据源
         type: com.alibaba.druid.pool.DruidDataSource
         # 配置获取连接等待超时的时间
@@ -95,9 +95,9 @@
     ## Redis服务器地址
     host: 127.0.0.1
     ## Redis服务器连接端口
-    port: 6380
+    port: 6379
     ## Redis服务器连接密码（默认为空）
-    password: root
+    password:
     jedis:
       pool:
         ## 连接池最大连接数（使用负值表示没有限制）
