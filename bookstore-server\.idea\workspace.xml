<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8c678adc-630e-4c1a-8756-2bdb7028ae84" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/huang/store/entity/order/Coupon.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/huang/store/entity/order/Expense.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/huang/store/entity/order/Expense.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/huang/store/entity/order/Order.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/huang/store/entity/order/Order.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/huang/store/entity/order/UserCoupon.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/huang/store/entity/order/UserCoupon.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/database/spike_system_update.sql" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/database/spike_system_update.sql" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\apache-maven-3.8.8-bin\repository" />
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\apache-maven-3.8.8-bin\apache-maven-3.8.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="#JAVA_HOME" />
    <option name="vmOptions" value="-Xms128m -Xmx512m -Duser.language=zh -Dfile.encoding=UTF-8" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2zDMvlPpza73Nd6nbO2eTgpY7aV" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.StoreApplication.executor": "Debug",
    "git-widget-placeholder": "main",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "E:/Personal/Homework/SE/bookstore-server",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "redis",
      "mysql"
    ]
  }
}]]></component>
  <component name="RunManager">
    <configuration name="StoreApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="store" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.huang.store.StoreApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8c678adc-630e-4c1a-8756-2bdb7028ae84" name="Changes" comment="" />
      <created>1751261423574</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751261423574</updated>
      <workItem from="1751261425249" duration="296000" />
      <workItem from="1751281915797" duration="57000" />
      <workItem from="1751330699613" duration="85000" />
      <workItem from="1751330800608" duration="234000" />
      <workItem from="1751331040775" duration="453000" />
      <workItem from="1751331518444" duration="176000" />
      <workItem from="1751331733988" duration="9532000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>