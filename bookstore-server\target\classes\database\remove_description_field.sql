-- ========================================
-- 删除优惠券描述字段迁移脚本
-- 执行时间：2024年12月
-- 说明：删除coupon_template表中冗余的description字段
-- ========================================

-- 检查字段是否存在，如果存在则删除
SET @sql = (
    SELECT IF(
        COUNT(*) > 0,
        'ALTER TABLE coupon_template DROP COLUMN description;',
        'SELECT "description字段不存在，无需删除" as message;'
    )
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'coupon_template' 
    AND COLUMN_NAME = 'description'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 重新创建用户可用优惠券视图（如果存在的话）
DROP VIEW IF EXISTS `user_available_coupons`;

CREATE VIEW `user_available_coupons` AS
SELECT
    uc.id as user_coupon_id,
    uc.account,
    uc.coupon_code,
    ct.name as coupon_name,
    ct.type,
    ct.discount_value,
    ct.min_order_amount,
    ct.max_discount_amount,
    uc.receive_time,
    uc.expire_time,
    CASE
        WHEN ct.type = 1 THEN CONCAT('满', ct.min_order_amount, '减', ct.discount_value)
        WHEN ct.type = 2 THEN CONCAT(ct.discount_value/10, '折优惠')
    END as discount_desc
FROM user_coupon uc
JOIN coupon_template ct ON uc.coupon_template_id = ct.id
WHERE uc.status = 1
  AND uc.expire_time > NOW()
  AND ct.status = 1;

-- 显示完成信息
SELECT 'description字段清理完成！' as message;
