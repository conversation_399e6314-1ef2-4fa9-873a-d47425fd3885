<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huang.store.mapper.AddressMapper">
    <insert id="addAddress" parameterType="com.huang.store.entity.user.Address">
        insert into address(account,name,phone,addr,label)
            values (#{account},#{name},#{phone},#{addr},#{label})
    </insert>
    <delete id="deleteAddress">
        delete from address where id=#{id}
    </delete>
    <update id="modifyAddress" parameterType="com.huang.store.entity.user.Address">
        update address set name=#{name},
                           phone=#{phone},
                           addr=#{addr},
                           label=#{label}
                        where id=#{id}
    </update>
    <select id="addressList" resultType="com.huang.store.entity.user.Address">
        select * from address where account=#{account} and off=false
    </select>
    <update id="setAddressOff">
        update address set off=true where id=#{id}
    </update>
</mapper>