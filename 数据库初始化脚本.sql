-- 书店系统数据库初始化脚本
-- 请在MySQL命令行中执行此脚本

-- 1. 创建数据库
DROP DATABASE IF EXISTS bookstore;
CREATE DATABASE bookstore CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. 使用数据库
USE bookstore;

-- 3. 显示创建结果
SHOW DATABASES LIKE 'bookstore';

-- 4. 显示当前数据库
SELECT DATABASE();

-- 执行完成后，请继续执行以下命令来创建表结构和插入数据：
-- source bookstore-server/src/main/resources/database/bookstore_ddl.sql
-- source bookstore-server/src/main/resources/database/bookstore_sample_data.sql
