# 优惠券系统扩展设计方案

## 📋 现状分析

### 当前系统状态
- **现有字段**：`expense` 表中只有一个简单的 `coupon` 字段（int类型）
- **功能缺失**：缺乏完整的优惠券管理、发放、使用、统计功能
- **扩展需求**：需要支持多种优惠券类型、用户领取、使用限制等

## 🎯 设计目标

1. **功能完整性**：支持优惠券的全生命周期管理
2. **灵活性**：支持多种优惠券类型和规则
3. **可扩展性**：便于后续功能扩展
4. **性能优化**：合理的索引和查询优化
5. **数据一致性**：保证优惠券使用的准确性

## 🏗️ 扩展方案

### 方案选择：**新建表 + 修改现有表**

#### 原因分析：
1. **现有字段不足**：单个 `coupon` 字段无法满足复杂的优惠券业务
2. **业务复杂性**：优惠券涉及模板管理、用户领取、使用记录等多个维度
3. **数据完整性**：需要完整的关联关系和约束

## 📊 数据库设计

### 1. 新建表结构

#### 1.1 优惠券模板表 (`coupon_template`)
**用途**：管理员创建的优惠券类型和规则

| 字段 | 类型 | 说明 |
|------|------|------|
| id | int(11) | 主键，模板ID |
| name | varchar(100) | 优惠券名称 |
| description | varchar(255) | 优惠券描述 |
| type | tinyint(2) | 类型：1-满减券，2-折扣券，3-免邮券 |
| discount_type | tinyint(2) | 折扣类型：1-固定金额，2-百分比 |
| discount_value | decimal(10,2) | 折扣值 |
| min_order_amount | decimal(10,2) | 最低消费金额 |
| max_discount_amount | decimal(10,2) | 最大折扣金额 |
| total_quantity | int(11) | 发放总数量 |
| used_quantity | int(11) | 已使用数量 |
| per_user_limit | int(11) | 每用户限领数量 |
| valid_start_time | timestamp | 有效期开始时间 |
| valid_end_time | timestamp | 有效期结束时间 |
| status | tinyint(1) | 状态：0-停用，1-启用 |

#### 1.2 用户优惠券表 (`user_coupon`)
**用途**：用户领取的优惠券实例

| 字段 | 类型 | 说明 |
|------|------|------|
| id | int(11) | 主键，用户优惠券ID |
| coupon_template_id | int(11) | 优惠券模板ID |
| account | varchar(100) | 用户账号 |
| coupon_code | varchar(50) | 优惠券码 |
| status | tinyint(2) | 状态：1-未使用，2-已使用，3-已过期 |
| receive_time | timestamp | 领取时间 |
| use_time | timestamp | 使用时间 |
| order_id | varchar(50) | 使用的订单号 |
| expire_time | timestamp | 过期时间 |

#### 1.3 优惠券使用记录表 (`coupon_usage_log`)
**用途**：详细的优惠券使用记录

| 字段 | 类型 | 说明 |
|------|------|------|
| id | int(11) | 主键 |
| user_coupon_id | int(11) | 用户优惠券ID |
| order_id | varchar(50) | 订单号 |
| account | varchar(100) | 用户账号 |
| original_amount | decimal(10,2) | 原始金额 |
| discount_amount | decimal(10,2) | 优惠金额 |
| final_amount | decimal(10,2) | 最终金额 |
| use_time | timestamp | 使用时间 |

### 2. 修改现有表

#### 2.1 修改 `expense` 表
**新增字段**：
- `coupon_id`：使用的优惠券ID
- `coupon_discount`：优惠券折扣金额

#### 2.2 修改 `bookorder` 表（可选）
**新增字段**：
- `coupon_ids`：使用的优惠券ID列表

## 🔄 业务流程

### 1. 优惠券创建流程
1. 管理员在后台创建优惠券模板
2. 设置优惠券类型、折扣规则、有效期等
3. 系统生成优惠券模板记录

### 2. 优惠券发放流程
1. 用户满足条件（注册、活动等）
2. 系统自动或用户手动领取优惠券
3. 生成用户优惠券记录，分配唯一优惠券码

### 3. 优惠券使用流程
1. 用户下单时选择可用优惠券
2. 系统验证优惠券有效性和使用条件
3. 计算折扣金额，更新订单金额
4. 标记优惠券为已使用，记录使用日志

## 💡 优惠券类型设计

### 1. 满减券 (type=1)
- **规则**：订单满X元减Y元
- **示例**：满200元减30元
- **字段设置**：
  - `discount_type = 1`（固定金额）
  - `discount_value = 30.00`
  - `min_order_amount = 200.00`

### 2. 折扣券 (type=2)
- **规则**：订单享受X折优惠，最多减Y元
- **示例**：8折优惠，最多减50元
- **字段设置**：
  - `discount_type = 2`（百分比）
  - `discount_value = 80.00`（表示8折）
  - `max_discount_amount = 50.00`

### 3. 免邮券 (type=3)
- **规则**：订单免运费
- **示例**：免10元运费
- **字段设置**：
  - `discount_type = 1`（固定金额）
  - `discount_value = 10.00`
  - `min_order_amount = 0.00`

## 🚀 实施步骤

### 阶段1：数据库扩展
1. 执行 `优惠券系统扩展.sql` 脚本
2. 创建新表和修改现有表
3. 插入示例数据进行测试

### 阶段2：后端开发
1. 创建优惠券相关的Entity类
2. 开发优惠券管理的Service和Controller
3. 实现优惠券计算逻辑
4. 修改订单处理流程

### 阶段3：前端开发
1. 优惠券管理界面（管理员）
2. 用户优惠券中心
3. 下单页面优惠券选择
4. 优惠券使用记录查看

### 阶段4：测试和优化
1. 功能测试
2. 性能测试
3. 数据一致性测试
4. 索引优化

## 📈 性能优化建议

### 1. 索引策略
- 用户查询可用优惠券：`(account, status, expire_time)`
- 优惠券模板查询：`(status, valid_start_time, valid_end_time)`
- 使用记录查询：`(order_id, account)`

### 2. 缓存策略
- 热门优惠券模板缓存
- 用户可用优惠券列表缓存
- 优惠券使用规则缓存

### 3. 数据清理
- 定期清理过期优惠券
- 归档历史使用记录
- 优化查询性能

## 🔒 安全考虑

1. **优惠券码唯一性**：确保优惠券码不重复
2. **使用限制**：防止重复使用和超限使用
3. **金额校验**：严格校验折扣计算逻辑
4. **并发控制**：防止并发使用导致的数据不一致

## 📊 监控指标

1. **发放统计**：优惠券发放数量、类型分布
2. **使用统计**：使用率、转化率、平均折扣
3. **用户行为**：领取偏好、使用习惯
4. **业务影响**：对订单金额、用户留存的影响

这个设计方案提供了完整的优惠券系统架构，既保持了与现有系统的兼容性，又具备了良好的扩展性和灵活性。
