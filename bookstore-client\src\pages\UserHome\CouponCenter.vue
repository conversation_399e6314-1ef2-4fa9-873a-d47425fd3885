<template>
  <div class="coupon-center">
    <div class="header">
      <h2>优惠券中心</h2>
    </div>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="可领取" name="available">
        <div class="coupon-grid">
          <div 
            v-for="template in availableTemplates" 
            :key="template.id"
            class="coupon-card available-coupon">
            <div class="coupon-header">
              <div class="coupon-amount">
                <span class="amount">{{ formatAmount(template) }}</span>
                <span class="unit">{{ template.type === 1 ? '元' : '折' }}</span>
              </div>
              <div class="coupon-type">{{ template.typeDesc }}</div>
            </div>
            <div class="coupon-body">
              <h4>{{ template.name }}</h4>
              <p class="description">{{ template.description }}</p>
              <p class="condition">{{ template.discountDesc }}</p>
              <div class="coupon-info">
                <span>剩余: {{ template.remainingQuantity }}</span>
                <span>有效期: {{ template.validDays }}天</span>
              </div>
            </div>
            <div class="coupon-footer">
              <el-button 
                type="primary" 
                size="small" 
                @click="claimCoupon(template)"
                :disabled="template.remainingQuantity <= 0">
                {{ template.remainingQuantity <= 0 ? '已抢完' : '立即领取' }}
              </el-button>
            </div>
          </div>
        </div>
        <div v-if="availableTemplates.length === 0" class="empty-state">
          <i class="el-icon-tickets"></i>
          <p>暂无可领取的优惠券</p>
        </div>
      </el-tab-pane>

      <el-tab-pane label="我的优惠券" name="my">
        <div class="filter-tabs">
          <el-radio-group v-model="couponFilter" @change="filterMyCoupons">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="unused">未使用</el-radio-button>
            <el-radio-button label="used">已使用</el-radio-button>
            <el-radio-button label="expired">已过期</el-radio-button>
          </el-radio-group>
        </div>

        <div class="coupon-list">
          <div 
            v-for="coupon in filteredMyCoupons" 
            :key="coupon.id"
            class="coupon-item"
            :class="getCouponClass(coupon)">
            <div class="coupon-left">
              <div class="coupon-amount">
                <span class="amount">{{ formatAmount(coupon) }}</span>
                <span class="unit">{{ coupon.type === 1 ? '元' : '折' }}</span>
              </div>
            </div>
            <div class="coupon-right">
              <div class="coupon-info">
                <h4>{{ coupon.couponName }}</h4>
                <p class="condition">{{ coupon.discountDesc }}</p>
                <p class="code">券码: {{ coupon.couponCode }}</p>
                <div class="time-info">
                  <span v-if="coupon.status === 1">
                    {{ formatExpireTime(coupon.expireTime) }}
                  </span>
                  <span v-else-if="coupon.status === 2">
                    使用时间: {{ formatTime(coupon.useTime) }}
                  </span>
                  <span v-else>
                    已过期
                  </span>
                </div>
              </div>
              <div class="coupon-status">
                <el-tag :type="getStatusType(coupon.status)">
                  {{ coupon.statusDesc }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
        <div v-if="filteredMyCoupons.length === 0" class="empty-state">
          <i class="el-icon-tickets"></i>
          <p>{{ getEmptyMessage() }}</p>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { 
  getAvailableCouponTemplates, 
  getUserCoupons, 
  claimCoupon,
  formatRemainingTime,
  isExpiringSoon 
} from '@/api/coupon'

export default {
  name: 'CouponCenter',
  data() {
    return {
      activeTab: 'available',
      availableTemplates: [],
      myCoupons: [],
      filteredMyCoupons: [],
      couponFilter: 'all',
      loading: false
    }
  },
  mounted() {
    this.loadAvailableTemplates()
    this.loadMyCoupons()
  },
  methods: {
    async loadAvailableTemplates() {
      this.loading = true
      try {
        const response = await getAvailableCouponTemplates()
        if (response.data.code === 200) {
          this.availableTemplates = response.data.data
        } else {
          this.$message.error('加载可领取优惠券失败')
        }
      } catch (error) {
        console.error('加载可领取优惠券失败:', error)
        this.$message.error('加载可领取优惠券失败')
      } finally {
        this.loading = false
      }
    },

    async loadMyCoupons() {
      try {
        const response = await getUserCoupons()
        if (response.data.code === 200) {
          this.myCoupons = response.data.data
          this.filterMyCoupons()
        } else {
          this.$message.error('加载我的优惠券失败')
        }
      } catch (error) {
        console.error('加载我的优惠券失败:', error)
        this.$message.error('加载我的优惠券失败')
      }
    },

    async claimCoupon(template) {
      try {
        const response = await claimCoupon(template.id)
        if (response.data.code === 200) {
          this.$message.success('领取成功！')
          this.loadAvailableTemplates()
          this.loadMyCoupons()
        } else {
          this.$message.error(response.data.message || '领取失败')
        }
      } catch (error) {
        console.error('领取优惠券失败:', error)
        this.$message.error('领取失败')
      }
    },

    handleTabClick(tab) {
      if (tab.name === 'my') {
        this.loadMyCoupons()
      } else if (tab.name === 'available') {
        this.loadAvailableTemplates()
      }
    },

    filterMyCoupons() {
      if (this.couponFilter === 'all') {
        this.filteredMyCoupons = this.myCoupons
      } else if (this.couponFilter === 'unused') {
        this.filteredMyCoupons = this.myCoupons.filter(c => c.status === 1)
      } else if (this.couponFilter === 'used') {
        this.filteredMyCoupons = this.myCoupons.filter(c => c.status === 2)
      } else if (this.couponFilter === 'expired') {
        this.filteredMyCoupons = this.myCoupons.filter(c => c.status === 3)
      }
    },

    formatAmount(item) {
      if (item.type === 1) {
        return item.discountValue
      } else {
        return (item.discountValue / 10).toFixed(1)
      }
    },

    formatExpireTime(expireTime) {
      if (isExpiringSoon(expireTime)) {
        return `即将过期: ${formatRemainingTime(expireTime)}`
      } else {
        return `有效期至: ${this.formatTime(expireTime)}`
      }
    },

    formatTime(timeStr) {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN')
    },

    getCouponClass(coupon) {
      if (coupon.status === 3) return 'expired'
      if (coupon.status === 2) return 'used'
      if (isExpiringSoon(coupon.expireTime)) return 'expiring'
      return 'available'
    },

    getStatusType(status) {
      const typeMap = {
        1: 'success',
        2: 'info',
        3: 'danger'
      }
      return typeMap[status] || 'default'
    },

    getEmptyMessage() {
      const messageMap = {
        all: '暂无优惠券',
        unused: '暂无未使用的优惠券',
        used: '暂无已使用的优惠券',
        expired: '暂无已过期的优惠券'
      }
      return messageMap[this.couponFilter] || '暂无优惠券'
    }
  }
}
</script>

<style scoped>
.coupon-center {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header h2 {
  margin: 0 0 20px 0;
  color: #303133;
}

.coupon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.coupon-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
}

.coupon-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.available-coupon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.coupon-header {
  padding: 20px;
  text-align: center;
}

.coupon-amount .amount {
  font-size: 36px;
  font-weight: bold;
}

.coupon-amount .unit {
  font-size: 18px;
  margin-left: 5px;
}

.coupon-type {
  margin-top: 5px;
  opacity: 0.8;
}

.coupon-body {
  padding: 0 20px 20px;
}

.coupon-body h4 {
  margin: 0 0 10px 0;
  font-size: 18px;
}

.description {
  margin: 0 0 10px 0;
  opacity: 0.8;
  font-size: 14px;
}

.condition {
  margin: 0 0 15px 0;
  font-weight: bold;
}

.coupon-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  opacity: 0.8;
}

.coupon-footer {
  padding: 0 20px 20px;
  text-align: center;
}

.filter-tabs {
  margin-bottom: 20px;
}

.coupon-list {
  space-y: 15px;
}

.coupon-item {
  display: flex;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
  transition: all 0.3s;
}

.coupon-item.available {
  border-left: 4px solid #67c23a;
}

.coupon-item.expiring {
  border-left: 4px solid #e6a23c;
}

.coupon-item.used {
  border-left: 4px solid #909399;
  opacity: 0.7;
}

.coupon-item.expired {
  border-left: 4px solid #f56c6c;
  opacity: 0.5;
}

.coupon-left {
  width: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.coupon-right {
  flex: 1;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.coupon-info p {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #606266;
}

.code {
  font-family: monospace;
  font-size: 12px !important;
  color: #909399 !important;
}

.time-info {
  font-size: 12px;
  color: #909399;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}
</style>
