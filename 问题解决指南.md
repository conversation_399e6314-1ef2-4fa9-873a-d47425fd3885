# 书店系统环境配置问题解决指南

## 🚨 当前问题

后端启动失败，错误信息：
```
Access denied for user 'root'@'localhost' (using password: YES)
```

## 📋 环境状态检查

✅ **已完成的配置**：
- Java 21.0.5 (兼容JDK 17要求)
- Node.js v23.9.0
- Maven 3.8.8
- MySQL 8.4.4
- Redis ********
- 后端项目编译成功
- 前端依赖安装完成
- Redis服务已启动（端口6380，密码root）

❌ **需要解决的问题**：
- MySQL数据库连接失败
- 数据库和表结构未创建

## 🔧 解决步骤

### 步骤1: 启动MySQL服务

```bash
# 检查MySQL服务状态
sc query mysql

# 如果服务未启动，启动MySQL服务
net start mysql
```

### 步骤2: 确认MySQL root密码

项目配置文件中设置的密码是 `root`，您需要确认您的MySQL root密码是否为 `root`。

**方法1: 测试当前密码**
```bash
mysql -u root -p
# 输入密码：root
```

**方法2: 如果密码不是root，重置密码**
```bash
# 停止MySQL服务
net stop mysql

# 以安全模式启动MySQL
mysqld --skip-grant-tables

# 在新的命令行窗口中连接MySQL
mysql -u root

# 重置密码
USE mysql;
UPDATE user SET authentication_string=PASSWORD('root') WHERE User='root';
FLUSH PRIVILEGES;
EXIT;

# 重启MySQL服务
net stop mysql
net start mysql
```

**方法3: 修改项目配置文件**
如果您不想修改MySQL密码，可以修改项目配置：

编辑 `bookstore-server/src/main/resources/application-dev.yml`：
```yaml
spring:
  shardingsphere:
    datasource:
      master:
        password: 您的实际MySQL密码
      slave:
        password: 您的实际MySQL密码
```

### 步骤3: 创建数据库和表结构

**3.1 创建数据库**
```bash
mysql -u root -p
```

在MySQL命令行中执行：
```sql
-- 创建数据库
DROP DATABASE IF EXISTS bookstore;
CREATE DATABASE bookstore CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 验证创建成功
SHOW DATABASES LIKE 'bookstore';

-- 退出MySQL
EXIT;
```

**3.2 导入表结构**
```bash
# 导入DDL脚本（创建表结构）
mysql -u root -p bookstore < bookstore-server/src/main/resources/database/bookstore_ddl.sql
```

**3.3 导入示例数据**
```bash
# 导入示例数据
mysql -u root -p bookstore < bookstore-server/src/main/resources/database/bookstore_sample_data.sql
```

**3.4 验证数据导入**
```bash
mysql -u root -p
```

```sql
USE bookstore;
SHOW TABLES;
SELECT COUNT(*) FROM user;
SELECT COUNT(*) FROM book;
EXIT;
```

### 步骤4: 启动项目

**4.1 启动后端服务**
```bash
cd bookstore-server
mvn spring-boot:run
```

**4.2 启动前端服务**
```bash
cd bookstore-client
npm run dev
```

## 🎯 快速解决方案

如果您想快速解决问题，建议按以下顺序执行：

1. **确保MySQL服务运行**：`net start mysql`
2. **测试MySQL连接**：`mysql -u root -p`（输入您的密码）
3. **如果连接失败**：重置MySQL root密码为 `root`
4. **创建数据库**：执行上面的SQL命令
5. **导入数据**：执行导入脚本
6. **启动项目**：先启动后端，再启动前端

## 📞 需要帮助？

如果您在执行过程中遇到问题，请告诉我：

1. **MySQL服务状态**：`sc query mysql` 的输出结果
2. **MySQL连接测试**：能否成功连接到MySQL
3. **您的MySQL root密码**：是否为 `root`
4. **错误信息**：具体的错误提示

## 🚀 成功标志

当看到以下信息时，说明配置成功：

**后端启动成功**：
```
Tomcat started on port(s): 8080 (http)
Started StoreApplication in X.XXX seconds
```

**前端启动成功**：
```
App running at:
- Local: http://localhost:8081/
```

## 📝 默认测试账号

系统启动成功后，可以使用以下账号登录：

- **管理员**：<EMAIL> / 123456
- **普通用户**：<EMAIL> / 123456

---

**下一步**：解决MySQL连接问题后，我们就可以成功启动整个系统了！
